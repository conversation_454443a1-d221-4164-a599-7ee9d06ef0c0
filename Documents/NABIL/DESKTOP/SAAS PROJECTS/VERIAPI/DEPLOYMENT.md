# Guía de Despliegue de VeriAPI

Esta guía te ayudará a desplegar VeriAPI en diferentes entornos de producción.

## 🚀 Despliegue Rápido con Docker

### Prerrequisitos
- Docker y Docker Compose instalados
- <PERSON><PERSON><PERSON> configurado (opcional)

### 1. <PERSON><PERSON><PERSON> docker-compose.yml

```yaml
version: '3.8'

services:
  mongodb:
    image: mongo:5.0
    container_name: veriapi-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: veriapi
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD}
      MONGO_INITDB_DATABASE: veriapi
    volumes:
      - mongodb_data:/data/db
    networks:
      - veriapi-network

  api:
    build: ./backend
    container_name: veriapi-api
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      DATABASE_URL: mongodb://veriapi:${MONGO_PASSWORD}@mongodb:27017/veriapi
      JWT_SECRET: ${JWT_SECRET}
      PORT: 3000
    depends_on:
      - mongodb
    networks:
      - veriapi-network
    volumes:
      - ./backend/uploads:/app/uploads

  nginx:
    image: nginx:alpine
    container_name: veriapi-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api
    networks:
      - veriapi-network

volumes:
  mongodb_data:

networks:
  veriapi-network:
    driver: bridge
```

### 2. Crear Dockerfile para el backend

```dockerfile
FROM node:18-alpine

WORKDIR /app

# Instalar dependencias del sistema para Puppeteer
RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    freetype-dev \
    harfbuzz \
    ca-certificates \
    ttf-freefont

# Configurar Puppeteer para usar Chromium instalado
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser

# Copiar package.json y package-lock.json
COPY package*.json ./

# Instalar dependencias
RUN npm ci --only=production

# Copiar código fuente
COPY . .

# Crear directorio para uploads
RUN mkdir -p uploads

# Exponer puerto
EXPOSE 3000

# Usuario no root para seguridad
RUN addgroup -g 1001 -S nodejs
RUN adduser -S veriapi -u 1001
USER veriapi

# Comando de inicio
CMD ["npm", "start"]
```

### 3. Configurar variables de entorno

```bash
# Crear archivo .env
echo "MONGO_PASSWORD=$(openssl rand -base64 32)" > .env
echo "JWT_SECRET=$(openssl rand -base64 64)" >> .env
```

### 4. Desplegar

```bash
docker-compose up -d
```

## 🌐 Despliegue en VPS/Servidor Dedicado

### 1. Preparar el servidor

```bash
# Actualizar sistema (Ubuntu/Debian)
sudo apt update && sudo apt upgrade -y

# Instalar Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Instalar MongoDB
wget -qO - https://www.mongodb.org/static/pgp/server-5.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/5.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-5.0.list
sudo apt-get update
sudo apt-get install -y mongodb-org

# Instalar PM2
sudo npm install -g pm2

# Instalar Nginx
sudo apt install -y nginx

# Instalar Certbot para SSL
sudo apt install -y certbot python3-certbot-nginx
```

### 2. Configurar MongoDB

```bash
# Iniciar MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod

# Crear usuario de base de datos
mongo
> use admin
> db.createUser({
    user: "veriapi",
    pwd: "password-muy-seguro",
    roles: ["readWriteAnyDatabase"]
  })
> exit
```

### 3. Configurar la aplicación

```bash
# Clonar repositorio
git clone https://github.com/tu-usuario/veriapi.git
cd veriapi/backend

# Instalar dependencias
npm install --production

# Configurar variables de entorno
cp .env.example .env
# Editar .env con configuración de producción

# Iniciar con PM2
pm2 start src/app.js --name veriapi
pm2 startup
pm2 save
```

### 4. Configurar Nginx

```nginx
# /etc/nginx/sites-available/veriapi
server {
    listen 80;
    server_name api.tudominio.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Configuración para archivos grandes
        client_max_body_size 10M;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Servir archivos estáticos directamente
    location /uploads/ {
        alias /ruta/a/veriapi/backend/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

```bash
# Activar sitio
sudo ln -s /etc/nginx/sites-available/veriapi /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

# Configurar SSL
sudo certbot --nginx -d api.tudominio.com
```

## ☁️ Despliegue en la Nube

### AWS EC2 + MongoDB Atlas

1. **Crear instancia EC2**
   - Ubuntu 20.04 LTS
   - t3.small o superior
   - Configurar Security Groups (puertos 22, 80, 443)

2. **Configurar MongoDB Atlas**
   - Crear cluster gratuito
   - Configurar usuario y whitelist de IPs
   - Obtener connection string

3. **Desplegar aplicación**
   ```bash
   # En la instancia EC2
   git clone https://github.com/tu-usuario/veriapi.git
   cd veriapi/backend
   npm install --production
   
   # Configurar .env con MongoDB Atlas
   DATABASE_URL=mongodb+srv://usuario:<EMAIL>/veriapi
   
   pm2 start src/app.js --name veriapi
   ```

### DigitalOcean App Platform

1. **Crear aplicación**
   - Conectar repositorio GitHub
   - Configurar build command: `npm install`
   - Configurar run command: `npm start`

2. **Configurar base de datos**
   - Crear MongoDB Managed Database
   - Configurar variables de entorno

3. **Configurar dominio personalizado**

### Heroku

```bash
# Instalar Heroku CLI
npm install -g heroku

# Login y crear app
heroku login
heroku create veriapi-tu-nombre

# Configurar MongoDB
heroku addons:create mongolab:sandbox

# Configurar variables de entorno
heroku config:set JWT_SECRET=$(openssl rand -base64 64)
heroku config:set NODE_ENV=production

# Desplegar
git push heroku main
```

## 🔒 Configuración de Seguridad

### 1. Firewall

```bash
# UFW (Ubuntu)
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### 2. Fail2Ban

```bash
sudo apt install fail2ban

# Configurar para Nginx
sudo nano /etc/fail2ban/jail.local
```

```ini
[nginx-http-auth]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 10
```

### 3. Backup automático

```bash
# Script de backup
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mongodump --host localhost --db veriapi --out /backup/mongodb_$DATE
tar -czf /backup/veriapi_$DATE.tar.gz /path/to/veriapi
aws s3 cp /backup/veriapi_$DATE.tar.gz s3://tu-bucket/backups/
```

## 📊 Monitoreo

### 1. PM2 Monitoring

```bash
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
```

### 2. Nginx Logs

```bash
# Configurar logrotate
sudo nano /etc/logrotate.d/nginx
```

### 3. Health Checks

```bash
# Script de health check
#!/bin/bash
curl -f http://localhost:3000/health || exit 1
```

## 🚀 Optimizaciones de Rendimiento

### 1. Nginx Caching

```nginx
# Configurar cache
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=api_cache:10m max_size=1g inactive=60m use_temp_path=off;

location /api/ {
    proxy_cache api_cache;
    proxy_cache_valid 200 5m;
    proxy_cache_use_stale error timeout updating http_500 http_502 http_503 http_504;
}
```

### 2. Compresión

```nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
```

### 3. MongoDB Indexing

```javascript
// Crear índices adicionales
db.invoices.createIndex({ "company": 1, "issueDate": -1 })
db.customers.createIndex({ "company": 1, "name": "text" })
```

## 📈 Escalabilidad

### Load Balancer con Nginx

```nginx
upstream veriapi_backend {
    server 127.0.0.1:3000;
    server 127.0.0.1:3001;
    server 127.0.0.1:3002;
}

server {
    location / {
        proxy_pass http://veriapi_backend;
    }
}
```

### Cluster Mode

```javascript
// cluster.js
const cluster = require('cluster');
const numCPUs = require('os').cpus().length;

if (cluster.isMaster) {
    for (let i = 0; i < numCPUs; i++) {
        cluster.fork();
    }
} else {
    require('./src/app.js');
}
```

## 🔧 Mantenimiento

### Actualizaciones

```bash
# Backup antes de actualizar
pm2 stop veriapi
mongodump --db veriapi --out backup_$(date +%Y%m%d)

# Actualizar código
git pull origin main
npm install --production

# Reiniciar
pm2 restart veriapi
```

### Logs

```bash
# Ver logs en tiempo real
pm2 logs veriapi

# Logs de Nginx
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```
