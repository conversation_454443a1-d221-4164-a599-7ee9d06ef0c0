<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Factura {{invoiceNumber}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            border-bottom: 3px solid #2c5aa0;
            padding-bottom: 20px;
        }
        
        .company-info {
            flex: 1;
        }
        
        .company-name {
            font-size: 26px;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 12px;
        }
        
        .company-details {
            font-size: 11px;
            line-height: 1.4;
            color: #555;
        }
        
        .invoice-title {
            text-align: right;
            flex: 1;
        }
        
        .invoice-title h1 {
            font-size: 32px;
            color: #2c5aa0;
            margin-bottom: 12px;
            font-weight: 300;
        }
        
        .invoice-number {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }
        
        .invoice-dates {
            font-size: 11px;
            color: #666;
        }
        
        .billing-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 35px;
            gap: 30px;
        }
        
        .billing-info {
            flex: 1;
        }
        
        .billing-info h3 {
            font-size: 14px;
            color: #2c5aa0;
            margin-bottom: 12px;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 6px;
            font-weight: 600;
        }
        
        .billing-details {
            font-size: 11px;
            line-height: 1.5;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .table th,
        .table td {
            padding: 12px 8px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        .table th {
            background: linear-gradient(135deg, #2c5aa0 0%, #3d6bb3 100%);
            color: white;
            font-weight: 600;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .table td {
            font-size: 11px;
            background: white;
        }
        
        .table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .table tbody tr:hover {
            background-color: #e3f2fd;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-center {
            text-align: center;
        }
        
        .totals-section {
            margin-top: 25px;
            display: flex;
            justify-content: flex-end;
        }
        
        .totals-table {
            width: 350px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .totals-table td {
            padding: 8px 15px;
            border-bottom: 1px solid #e9ecef;
            font-size: 12px;
        }
        
        .totals-table tr:last-child td {
            border-bottom: none;
        }
        
        .totals-table .total-row {
            font-weight: bold;
            font-size: 16px;
            background: linear-gradient(135deg, #2c5aa0 0%, #3d6bb3 100%);
            color: white;
        }
        
        .tax-breakdown {
            margin-top: 25px;
            font-size: 11px;
        }
        
        .tax-breakdown h4 {
            color: #2c5aa0;
            margin-bottom: 12px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .footer {
            margin-top: 50px;
            padding-top: 25px;
            border-top: 2px solid #e9ecef;
            font-size: 10px;
            color: #666;
            text-align: center;
        }
        
        .payment-info {
            margin-top: 25px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-left: 5px solid #2c5aa0;
            border-radius: 0 8px 8px 0;
        }
        
        .payment-info h4 {
            color: #2c5aa0;
            margin-bottom: 12px;
            font-size: 14px;
            font-weight: 600;
        }
        
        .payment-details {
            font-size: 11px;
            line-height: 1.5;
        }
        
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .amount-highlight {
            background-color: #d4edda;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        @media print {
            .container {
                padding: 0;
                max-width: none;
            }
            
            body {
                font-size: 11px;
            }
            
            .header {
                border-bottom: 2px solid #2c5aa0;
            }
            
            .table {
                box-shadow: none;
            }
            
            .payment-info {
                background: #f8f9fa;
                border: 1px solid #e9ecef;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="company-info">
                <div class="company-name">{{company.name}}</div>
                {{#if company.tradeName}}
                <div style="font-size: 14px; color: #666; margin-bottom: 8px;">{{company.tradeName}}</div>
                {{/if}}
                <div class="company-details">
                    {{#if company.cif}}<strong>CIF:</strong> {{company.cif}}<br>{{/if}}
                    {{#if company.nif}}<strong>NIF:</strong> {{company.nif}}<br>{{/if}}
                    {{#if company.nie}}<strong>NIE:</strong> {{company.nie}}<br>{{/if}}
                    {{company.fullAddress}}<br>
                    {{#if company.contact.phone}}<strong>Tel:</strong> {{company.contact.phone}}<br>{{/if}}
                    {{#if company.contact.email}}<strong>Email:</strong> {{company.contact.email}}<br>{{/if}}
                    {{#if company.contact.website}}<strong>Web:</strong> {{company.contact.website}}{{/if}}
                </div>
            </div>
            <div class="invoice-title">
                <h1>FACTURA</h1>
                <div class="invoice-number">Nº {{invoiceNumber}}</div>
                <div class="invoice-dates">
                    <div><strong>Fecha:</strong> {{formatDate issueDate}}</div>
                    <div><strong>Vencimiento:</strong> {{formatDate dueDate}}</div>
                    {{#if serviceDate}}
                    <div><strong>Fecha Servicio:</strong> {{formatDate serviceDate}}</div>
                    {{/if}}
                </div>
            </div>
        </div>

        <!-- Billing Information -->
        <div class="billing-section">
            <div class="billing-info">
                <h3>Facturar a:</h3>
                <div class="billing-details">
                    <strong>{{customer.displayName}}</strong><br>
                    {{#if customer.taxId}}<strong>{{#if customer.cif}}CIF{{else}}{{#if customer.nif}}NIF{{else}}NIE{{/if}}{{/if}}:</strong> {{customer.taxId}}<br>{{/if}}
                    {{customer.fullAddress}}<br>
                    {{#if customer.contact.email}}<strong>Email:</strong> {{customer.contact.email}}<br>{{/if}}
                    {{#if customer.contact.phone}}<strong>Tel:</strong> {{customer.contact.phone}}{{/if}}
                </div>
            </div>
            {{#if externalReferences.orderNumber}}
            <div class="billing-info">
                <h3>Referencias:</h3>
                <div class="billing-details">
                    <strong>Nº Pedido:</strong> {{externalReferences.orderNumber}}<br>
                    {{#if externalReferences.woocommerceOrderId}}
                    <strong>WooCommerce ID:</strong> {{externalReferences.woocommerceOrderId}}<br>
                    {{/if}}
                </div>
            </div>
            {{/if}}
        </div>

        <!-- Invoice Lines -->
        <table class="table">
            <thead>
                <tr>
                    <th style="width: 40%;">Descripción</th>
                    <th class="text-center" style="width: 10%;">Cant.</th>
                    <th class="text-right" style="width: 15%;">Precio Unit.</th>
                    {{#if (gt totalDiscount 0)}}
                    <th class="text-right" style="width: 10%;">Desc.</th>
                    {{/if}}
                    <th class="text-right" style="width: 10%;">IVA</th>
                    <th class="text-right" style="width: 15%;">Importe</th>
                </tr>
            </thead>
            <tbody>
                {{#each lines}}
                <tr>
                    <td>
                        <strong>{{description}}</strong>
                        {{#if productCode}}<br><small style="color: #666;">Código: {{productCode}}</small>{{/if}}
                    </td>
                    <td class="text-center">{{formatNumber quantity}} {{unit}}</td>
                    <td class="text-right">{{formatCurrency unitPrice}}</td>
                    {{#if (gt ../totalDiscount 0)}}
                    <td class="text-right">{{#if (gt discount 0)}}{{formatNumber discount}}%{{else}}-{{/if}}</td>
                    {{/if}}
                    <td class="text-right">{{formatNumber taxRate}}%</td>
                    <td class="text-right"><strong>{{formatCurrency total}}</strong></td>
                </tr>
                {{/each}}
            </tbody>
        </table>

        <!-- Totals -->
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td><strong>Subtotal:</strong></td>
                    <td class="text-right">{{formatCurrency subtotal}}</td>
                </tr>
                {{#if (gt totalDiscount 0)}}
                <tr>
                    <td><strong>Descuento:</strong></td>
                    <td class="text-right">-{{formatCurrency totalDiscount}}</td>
                </tr>
                {{/if}}
                <tr>
                    <td><strong>Base Imponible:</strong></td>
                    <td class="text-right">{{formatCurrency netAmount}}</td>
                </tr>
                <tr>
                    <td><strong>IVA:</strong></td>
                    <td class="text-right">{{formatCurrency totalTax}}</td>
                </tr>
                {{#if (gt compliance.irpfAmount 0)}}
                <tr>
                    <td><strong>IRPF ({{formatNumber compliance.irpfRate}}%):</strong></td>
                    <td class="text-right">-{{formatCurrency compliance.irpfAmount}}</td>
                </tr>
                {{/if}}
                <tr class="total-row">
                    <td><strong>TOTAL:</strong></td>
                    <td class="text-right"><strong>{{formatCurrency total}}</strong></td>
                </tr>
            </table>
        </div>

        <!-- Tax Breakdown -->
        {{#if taxBreakdown}}
        <div class="tax-breakdown">
            <h4>Desglose de IVA:</h4>
            <table class="table">
                <thead>
                    <tr>
                        <th>Tipo</th>
                        <th class="text-right">Base Imponible</th>
                        <th class="text-right">Tipo IVA</th>
                        <th class="text-right">Cuota IVA</th>
                    </tr>
                </thead>
                <tbody>
                    {{#each taxBreakdown}}
                    <tr>
                        <td><strong>{{taxName}}</strong></td>
                        <td class="text-right">{{formatCurrency taxableAmount}}</td>
                        <td class="text-right">{{formatNumber taxRate}}%</td>
                        <td class="text-right"><strong>{{formatCurrency taxAmount}}</strong></td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
        </div>
        {{/if}}

        <!-- Payment Information -->
        {{#if company.bankInfo.iban}}
        <div class="payment-info">
            <h4>💳 Información de Pago</h4>
            <div class="payment-details">
                <strong>IBAN:</strong> <span class="highlight">{{company.bankInfo.iban}}</span><br>
                {{#if company.bankInfo.bankName}}<strong>Banco:</strong> {{company.bankInfo.bankName}}<br>{{/if}}
                {{#if company.bankInfo.swift}}<strong>SWIFT:</strong> {{company.bankInfo.swift}}<br>{{/if}}
                <strong>Fecha de Vencimiento:</strong> <span class="amount-highlight">{{formatDate dueDate}}</span>
            </div>
        </div>
        {{/if}}

        <!-- Notes -->
        {{#if notes}}
        <div class="payment-info">
            <h4>📝 Observaciones</h4>
            <div class="payment-details">{{notes}}</div>
        </div>
        {{/if}}

        <!-- Footer -->
        <div class="footer">
            <div>
                Esta factura ha sido generada electrónicamente por <strong>VeriAPI</strong> - Sistema de Facturación Electrónica
                {{#if compliance.reverseCharge}}
                <br><strong>⚠️ Inversión del sujeto pasivo - Reverse Charge</strong>
                {{/if}}
                {{#if compliance.specialRegime}}
                {{#unless (eq compliance.specialRegime 'general')}}
                <br><strong>Régimen Especial:</strong> {{compliance.specialRegime}}
                {{/unless}}
                {{/if}}
            </div>
        </div>
    </div>
</body>
</html>
