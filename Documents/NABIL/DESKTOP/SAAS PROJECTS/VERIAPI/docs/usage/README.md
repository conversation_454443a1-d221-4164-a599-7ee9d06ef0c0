# Guía de Uso de VeriAPI

Esta guía te ayudará a usar VeriAPI para gestionar tu facturación electrónica de manera eficiente.

## Primeros Pasos

### 1. Configuración Inicial

Después de la instalación, configura tu empresa:

1. **Datos de la empresa**: Nombre, NIF/CIF, dirección fiscal
2. **Configuración fiscal**: Tipos de IVA, IRPF, régimen especial
3. **Información bancaria**: IBAN para pagos
4. **Configuración de facturas**: Prefijo, numeración, días de vencimiento

### 2. Configuración de Tipos de IVA

VeriAPI incluye los tipos de IVA españoles por defecto:

- **IVA General (21%)**: Aplicable a la mayoría de productos y servicios
- **IVA Reducido (10%)**: Transporte, hostelería, algunos alimentos
- **IVA Superreducido (4%)**: Productos básicos, medicamentos, libros
- **Exento (0%)**: Servicios financieros, educación, sanidad

Puedes personalizar estos tipos según tu negocio.

## Gestión de Clientes

### Crear Clientes

#### Cliente Particular

```json
{
  "name": "Juan García López",
  "customerType": "individual",
  "nif": "12345678Z",
  "address": {
    "street": "Calle Mayor 123",
    "city": "Madrid",
    "postalCode": "28001",
    "country": "España"
  },
  "contact": {
    "email": "<EMAIL>",
    "phone": "+34 91 123 45 67"
  }
}
```

#### Cliente Empresa

```json
{
  "name": "Tecnología Avanzada S.L.",
  "customerType": "company",
  "cif": "B12345678",
  "address": {
    "street": "Polígono Industrial 45",
    "city": "Barcelona",
    "postalCode": "08001",
    "country": "España"
  },
  "contact": {
    "email": "<EMAIL>",
    "phone": "+34 93 123 45 67"
  },
  "taxInfo": {
    "isVatExempt": false,
    "reverseCharge": false
  }
}
```

### Sincronización con WooCommerce

El plugin de WordPress sincroniza automáticamente:

1. **Clientes nuevos**: Se crean automáticamente en VeriAPI
2. **Datos fiscales**: NIF/CIF desde campos personalizados
3. **Direcciones**: Dirección de facturación de WooCommerce
4. **Información de contacto**: Email y teléfono

## Creación de Facturas

### Factura Simple

```json
{
  "customer": "customer_id",
  "lines": [
    {
      "description": "Desarrollo de sitio web",
      "quantity": 1,
      "unitPrice": 1000.00,
      "taxRate": 21
    }
  ],
  "notes": "Pago por transferencia bancaria"
}
```

### Factura con Múltiples Líneas

```json
{
  "customer": "customer_id",
  "lines": [
    {
      "description": "Consultoría técnica",
      "quantity": 20,
      "unitPrice": 75.00,
      "taxRate": 21,
      "productCode": "CONS-001"
    },
    {
      "description": "Hosting anual",
      "quantity": 1,
      "unitPrice": 200.00,
      "taxRate": 21,
      "discount": 10
    },
    {
      "description": "Dominio .com",
      "quantity": 1,
      "unitPrice": 15.00,
      "taxRate": 21
    }
  ],
  "dueDate": "2024-02-15",
  "notes": "Descuento del 10% en hosting por cliente recurrente"
}
```

### Factura con IRPF

Para profesionales sujetos a retención:

```json
{
  "customer": "customer_id",
  "lines": [
    {
      "description": "Servicios de consultoría",
      "quantity": 1,
      "unitPrice": 2000.00,
      "taxRate": 21
    }
  ],
  "compliance": {
    "irpfRate": 15,
    "specialRegime": "general"
  }
}
```

### Factura Intracomunitaria

Para clientes de la UE:

```json
{
  "customer": "customer_id_eu",
  "lines": [
    {
      "description": "Software license",
      "quantity": 1,
      "unitPrice": 500.00,
      "taxRate": 0
    }
  ],
  "compliance": {
    "reverseCharge": true,
    "specialRegime": "general"
  },
  "notes": "Operación intracomunitaria - Inversión del sujeto pasivo"
}
```

## Gestión de Pagos

### Registrar Pago Completo

```http
POST /api/invoices/{id}/payment
```

```json
{
  "amount": 1210.00,
  "paymentDate": "2024-01-20",
  "method": "transfer",
  "reference": "TRANS-123456"
}
```

### Pago Parcial

```json
{
  "amount": 605.00,
  "paymentDate": "2024-01-20",
  "method": "transfer",
  "reference": "TRANS-123456"
}
```

La factura quedará marcada como "Pago parcial" hasta completar el importe total.

## Estados de Facturas

### Flujo Normal

1. **Borrador** (`draft`): Factura creada pero no enviada
2. **Enviada** (`sent`): Factura enviada al cliente
3. **Vista** (`viewed`): Cliente ha visto la factura
4. **Pagada** (`paid`): Pago completado
5. **Vencida** (`overdue`): Factura no pagada después del vencimiento

### Gestión de Estados

```http
PATCH /api/invoices/{id}/status
```

```json
{
  "status": "sent"
}
```

## Integración con WooCommerce

### Configuración Automática

1. **Instalación**: Activa el plugin VeriAPI en WordPress
2. **Configuración**: Introduce URL de API y API Key
3. **Sincronización**: Los clientes se sincronizan automáticamente
4. **Facturas automáticas**: Se crean al completar pedidos

### Campos Fiscales Personalizados

El plugin añade campos al checkout:

- **NIF/CIF/NIE**: Identificación fiscal del cliente
- **Tipo de cliente**: Particular o empresa

### Flujo de Facturación

1. **Cliente realiza pedido** en WooCommerce
2. **Pedido cambia a "Completado"** (o "Procesando")
3. **Plugin crea cliente** en VeriAPI (si no existe)
4. **Plugin crea factura** automáticamente
5. **Factura disponible** en admin de WordPress y VeriAPI

## Plantillas de Facturas

### Personalización

Las facturas se generan con plantilla HTML/CSS personalizable:

- **Logo de empresa**: Sube tu logo desde la configuración
- **Colores corporativos**: Modifica los estilos CSS
- **Información adicional**: Añade campos personalizados

### Elementos de la Factura

- **Datos de la empresa**: Nombre, NIF/CIF, dirección, contacto
- **Datos del cliente**: Nombre, identificación fiscal, dirección
- **Líneas de factura**: Descripción, cantidad, precio, IVA
- **Totales**: Subtotal, descuentos, base imponible, IVA, total
- **Información de pago**: IBAN, vencimiento, método de pago
- **Desglose fiscal**: Detalle por tipos de IVA

## Cumplimiento Fiscal Español

### Requisitos Obligatorios

VeriAPI cumple con todos los requisitos legales:

- ✅ **Numeración consecutiva** de facturas
- ✅ **Datos identificativos** completos
- ✅ **Desglose de IVA** por tipos
- ✅ **Fecha de emisión** y vencimiento
- ✅ **Retención IRPF** cuando aplique
- ✅ **Operaciones intracomunitarias**

### Tipos de Régimen

- **General**: Régimen común del IVA
- **Simplificado**: Para pequeños empresarios
- **Agricultura**: Régimen especial agrícola
- **Bienes usados**: Margen de beneficio
- **Agencias de viaje**: Régimen especial

## Informes y Estadísticas

### Dashboard Principal

- **Facturas del mes**: Total y importe
- **Facturas pendientes**: Por cobrar
- **Clientes activos**: Número total
- **Ingresos**: Evolución mensual

### Exportaciones

- **PDF individual**: Cada factura
- **Listado Excel**: Facturas por período
- **Facturae XML**: Formato estándar (próximamente)
- **SII**: Suministro Inmediato de Información (próximamente)

## Automatizaciones

### Recordatorios de Pago

Configura recordatorios automáticos:

- **7 días antes** del vencimiento
- **En la fecha** de vencimiento
- **7 días después** del vencimiento
- **15 días después** del vencimiento

### Facturación Recurrente

Para servicios periódicos:

- **Mensual**: Hosting, suscripciones
- **Trimestral**: Servicios de mantenimiento
- **Anual**: Licencias, dominios

## Mejores Prácticas

### Numeración de Facturas

- Usa prefijos descriptivos: `FAC`, `FACT`, `F`
- Mantén secuencia anual: `FAC2024-001`
- No reutilices números de facturas anuladas

### Gestión de Clientes

- Verifica siempre el NIF/CIF antes de facturar
- Mantén actualizadas las direcciones fiscales
- Clasifica clientes por tipo (particular/empresa)

### Configuración de IVA

- Revisa periódicamente los tipos de IVA
- Configura productos exentos correctamente
- Aplica IRPF según la normativa vigente

### Seguridad

- Cambia la API Key periódicamente
- Usa HTTPS en producción
- Realiza copias de seguridad regulares

## Solución de Problemas Comunes

### Error de NIF/CIF Inválido

Verifica el formato:
- **NIF**: 8 dígitos + letra (12345678Z)
- **CIF**: Letra + 7 dígitos + dígito/letra (B12345678)
- **NIE**: X/Y/Z + 7 dígitos + letra (X1234567L)

### Factura No Se Crea Automáticamente

1. Verifica la configuración del plugin
2. Comprueba la conexión con la API
3. Revisa los logs de WordPress
4. Confirma que el estado del pedido es correcto

### PDF No Se Genera

1. Verifica que Puppeteer esté instalado
2. Comprueba los permisos del servidor
3. Revisa la configuración de memoria
4. Verifica la plantilla HTML

## Soporte y Recursos

- **Documentación completa**: `/docs/`
- **API Reference**: `/docs/api/`
- **Ejemplos de código**: `/examples/`
- **Soporte técnico**: <EMAIL>
- **Comunidad**: GitHub Discussions
