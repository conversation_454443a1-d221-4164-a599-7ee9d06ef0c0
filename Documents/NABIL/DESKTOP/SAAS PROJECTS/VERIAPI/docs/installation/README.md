# Guía de Instalación de VeriAPI

Esta guía te ayudará a instalar y configurar VeriAPI, el sistema de facturación electrónica para el mercado español.

## Requisitos del Sistema

### Backend API

- **Node.js**: 18.0 o superior
- **MongoDB**: 5.0 o superior (o PostgreSQL 13+)
- **Memoria RAM**: Mínimo 1GB, recomendado 2GB
- **Espacio en disco**: <PERSON><PERSON><PERSON> 1GB
- **Sistema operativo**: Linux, macOS, o Windows

### Plugin de WordPress

- **WordPress**: 5.0 o superior
- **WooCommerce**: 6.0 o superior
- **PHP**: 7.4 o superior
- **MySQL**: 5.7 o superior

## Instalación del Backend API

### 1. Clonar el Repositorio

```bash
git clone https://github.com/veriapi/veriapi.git
cd veriapi/backend
```

### 2. Instalar Dependencias

```bash
npm install
```

### 3. Configurar Variables de Entorno

```bash
cp .env.example .env
```

Edita el archivo `.env` con tu configuración:

```env
# Configuración básica
NODE_ENV=production
PORT=3000

# Base de datos
DATABASE_URL=mongodb://localhost:27017/veriapi

# JWT
JWT_SECRET=tu-clave-secreta-muy-segura-aqui
JWT_EXPIRES_IN=7d

# Configuración de la empresa por defecto
DEFAULT_COMPANY_NAME=Tu Empresa S.L.
DEFAULT_COMPANY_NIF=B12345678
DEFAULT_COMPANY_EMAIL=<EMAIL>
DEFAULT_COMPANY_ADDRESS=Calle Principal 123
DEFAULT_COMPANY_CITY=Madrid
DEFAULT_COMPANY_POSTAL_CODE=28001
```

### 4. Iniciar la Base de Datos

#### MongoDB (Recomendado)

```bash
# Ubuntu/Debian
sudo apt-get install mongodb

# macOS con Homebrew
brew install mongodb-community

# Iniciar MongoDB
sudo systemctl start mongod
```

#### PostgreSQL (Alternativo)

```bash
# Ubuntu/Debian
sudo apt-get install postgresql postgresql-contrib

# Crear base de datos
sudo -u postgres createdb veriapi
```

### 5. Iniciar el Servidor

```bash
# Desarrollo
npm run dev

# Producción
npm start
```

El servidor estará disponible en `http://localhost:3000`

### 6. Verificar la Instalación

Visita `http://localhost:3000/health` para verificar que el servidor esté funcionando correctamente.

## Instalación del Plugin de WordPress

### 1. Descargar el Plugin

Descarga la carpeta `wordpress-plugin` del repositorio de VeriAPI.

### 2. Instalar el Plugin

#### Método 1: Subida Manual

1. Comprime la carpeta `wordpress-plugin` en un archivo ZIP
2. Ve a **WordPress Admin > Plugins > Añadir nuevo**
3. Haz clic en **Subir plugin**
4. Selecciona el archivo ZIP y haz clic en **Instalar ahora**
5. Activa el plugin

#### Método 2: FTP

1. Sube la carpeta `wordpress-plugin` a `/wp-content/plugins/`
2. Renombra la carpeta a `veriapi-woocommerce`
3. Ve a **WordPress Admin > Plugins** y activa **VeriAPI - Facturación Electrónica**

### 3. Configurar el Plugin

1. Ve a **VeriAPI > Configuración** en el admin de WordPress
2. Configura la URL de tu API: `http://tu-servidor:3000`
3. Genera una API Key desde el backend de VeriAPI
4. Configura los datos de tu empresa
5. Guarda la configuración

## Configuración Inicial

### 1. Crear Usuario Administrador

Registra tu primera empresa y usuario a través de la API:

```bash
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "tu-password-seguro",
    "firstName": "Tu",
    "lastName": "Nombre",
    "companyName": "Tu Empresa S.L.",
    "companyEmail": "<EMAIL>",
    "companyAddress": "Calle Principal 123",
    "companyCity": "Madrid",
    "companyPostalCode": "28001",
    "companyCif": "B12345678"
  }'
```

### 2. Generar API Key

```bash
curl -X POST http://localhost:3000/api/auth/generate-api-key \
  -H "Authorization: Bearer TU_JWT_TOKEN"
```

### 3. Configurar Tipos de IVA

Los tipos de IVA por defecto se configuran automáticamente:
- IVA General: 21%
- IVA Reducido: 10%
- IVA Superreducido: 4%
- Exento: 0%

## Configuración de Producción

### 1. Usar un Gestor de Procesos

```bash
# Instalar PM2
npm install -g pm2

# Iniciar la aplicación
pm2 start src/app.js --name veriapi

# Configurar inicio automático
pm2 startup
pm2 save
```

### 2. Configurar Nginx (Recomendado)

```nginx
server {
    listen 80;
    server_name api.tudominio.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### 3. Configurar SSL

```bash
# Instalar Certbot
sudo apt-get install certbot python3-certbot-nginx

# Obtener certificado SSL
sudo certbot --nginx -d api.tudominio.com
```

### 4. Configurar Firewall

```bash
# UFW (Ubuntu)
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

## Configuración de Base de Datos en Producción

### MongoDB Atlas (Recomendado)

1. Crea una cuenta en [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Crea un cluster gratuito
3. Configura un usuario de base de datos
4. Obtén la cadena de conexión
5. Actualiza `DATABASE_URL` en tu `.env`

### MongoDB Local

```bash
# Configurar autenticación
mongo
> use admin
> db.createUser({
    user: "veriapi",
    pwd: "password-seguro",
    roles: ["readWriteAnyDatabase"]
  })

# Actualizar .env
DATABASE_URL=*********************************************************
```

## Monitoreo y Logs

### 1. Configurar Logs

```bash
# Ver logs con PM2
pm2 logs veriapi

# Configurar rotación de logs
pm2 install pm2-logrotate
```

### 2. Monitoreo de Salud

Configura un monitor para verificar `http://tu-servidor/health` regularmente.

## Solución de Problemas

### Error de Conexión a la Base de Datos

```bash
# Verificar estado de MongoDB
sudo systemctl status mongod

# Verificar logs
sudo journalctl -u mongod
```

### Error de Permisos

```bash
# Verificar permisos de archivos
ls -la
chmod 755 src/
```

### Puerto en Uso

```bash
# Verificar qué proceso usa el puerto
sudo lsof -i :3000

# Cambiar puerto en .env
PORT=3001
```

## Actualizaciones

### Backend

```bash
git pull origin main
npm install
pm2 restart veriapi
```

### Plugin de WordPress

1. Desactiva el plugin actual
2. Sube la nueva versión
3. Activa el plugin actualizado

## Soporte

- **Documentación**: `/docs/`
- **Issues**: GitHub Issues
- **Email**: <EMAIL>

## Próximos Pasos

Una vez instalado, consulta:
- [Guía de Uso](../usage/README.md)
- [Documentación de API](../api/README.md)
- [Configuración Avanzada](../advanced/README.md)
