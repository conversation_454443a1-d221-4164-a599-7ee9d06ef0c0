# Production Environment Configuration
NODE_ENV=production
PORT=3000

# Database Configuration (PostgreSQL with Neon.tech)
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"

# JWT Configuration (CHANGE IN PRODUCTION)
JWT_SECRET=veriapi-super-secret-production-key-change-this-immediately
JWT_EXPIRES_IN=7d

# Production URLs
API_BASE_URL=https://api.soloun.link
FRONTEND_URL=https://app.soloun.link

# CORS Configuration
CORS_ORIGIN=https://app.soloun.link,https://www.soloun.link

# Rate limiting (stricter in production)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=50

# PDF Generation
PDF_TIMEOUT=30000
PDF_WAIT_UNTIL=networkidle0

# Email Configuration (configure for production)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
FROM_EMAIL=<EMAIL>

# File upload settings
UPLOAD_MAX_SIZE=5242880
ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png

# Invoice settings
INVOICE_PREFIX=FAC
INVOICE_NUMBER_LENGTH=6
DEFAULT_DUE_DAYS=30
DEFAULT_TAX_RATE=21

# Currency and locale
DEFAULT_CURRENCY=EUR
DEFAULT_LOCALE=es-ES

# Logging
LOG_LEVEL=warn
LOG_FILE=logs/veriapi.log

# Security
BCRYPT_ROUNDS=12
API_KEY_LENGTH=40

# Company defaults
DEFAULT_COMPANY_NAME=VeriAPI Demo S.L.
DEFAULT_COMPANY_NIF=*********
DEFAULT_COMPANY_EMAIL=<EMAIL>
DEFAULT_COMPANY_ADDRESS=Calle Principal 123
DEFAULT_COMPANY_CITY=Madrid
DEFAULT_COMPANY_POSTAL_CODE=28001
DEFAULT_COMPANY_PHONE=+34 91 123 45 67

# Webhooks
WEBHOOK_SECRET=
WEBHOOK_TIMEOUT=5000
