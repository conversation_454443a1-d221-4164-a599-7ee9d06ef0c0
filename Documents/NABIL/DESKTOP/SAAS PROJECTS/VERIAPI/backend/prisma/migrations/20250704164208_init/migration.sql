-- CreateTable
CREATE TABLE "companies" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "name" VARCHAR(200) NOT NULL,
    "tradeName" VARCHAR(200),
    "nif" VARCHAR(9),
    "cif" VARCHAR(9),
    "nie" VARCHAR(9),
    "vatNumber" VARCHAR(20),
    "address" VARCHAR(255) NOT NULL,
    "city" VARCHAR(100) NOT NULL,
    "state" VARCHAR(100),
    "postalCode" VARCHAR(10) NOT NULL,
    "country" VARCHAR(2) NOT NULL DEFAULT 'ES',
    "contactPhone" VARCHAR(20),
    "contactMobile" VARCHAR(20),
    "contactEmail" VARCHAR(255) NOT NULL,
    "website" VARCHAR(255),
    "businessType" VARCHAR(50) NOT NULL DEFAULT 'autonomo',
    "sector" VARCHAR(100),
    "employees" INTEGER,
    "foundedYear" INTEGER,
    "taxRates" JSONB NOT NULL DEFAULT '[]',
    "invoicePrefix" VARCHAR(10) NOT NULL DEFAULT 'FAC',
    "invoiceNumberLength" INTEGER NOT NULL DEFAULT 6,
    "nextInvoiceNumber" INTEGER NOT NULL DEFAULT 1,
    "dueDays" INTEGER NOT NULL DEFAULT 30,
    "bankName" VARCHAR(100),
    "iban" VARCHAR(34),
    "swift" VARCHAR(11),
    "logo" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "subscriptionPlan" VARCHAR(20) NOT NULL DEFAULT 'free',
    "subscriptionStatus" VARCHAR(20) NOT NULL DEFAULT 'active',
    "subscriptionExpiresAt" TIMESTAMP(3),
    "subscriptionMaxInvoicesPerMonth" INTEGER NOT NULL DEFAULT 10,

    CONSTRAINT "companies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "email" VARCHAR(255) NOT NULL,
    "password" VARCHAR(255) NOT NULL,
    "firstName" VARCHAR(50) NOT NULL,
    "lastName" VARCHAR(50) NOT NULL,
    "role" VARCHAR(20) NOT NULL DEFAULT 'user',
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "lastLogin" TIMESTAMP(3),
    "apiKey" VARCHAR(100),
    "companyId" TEXT NOT NULL,
    "preferenceLanguage" VARCHAR(5) NOT NULL DEFAULT 'es',
    "preferenceTimezone" VARCHAR(50) NOT NULL DEFAULT 'Europe/Madrid',
    "preferenceCurrency" VARCHAR(3) NOT NULL DEFAULT 'EUR',
    "preferenceDateFormat" VARCHAR(20) NOT NULL DEFAULT 'DD/MM/YYYY',
    "notificationEmail" BOOLEAN NOT NULL DEFAULT true,
    "notificationInvoice" BOOLEAN NOT NULL DEFAULT true,
    "notificationPayment" BOOLEAN NOT NULL DEFAULT true,
    "resetPasswordToken" TEXT,
    "resetPasswordExpires" TIMESTAMP(3),
    "emailVerificationToken" TEXT,
    "emailVerified" BOOLEAN NOT NULL DEFAULT false,
    "emailVerifiedAt" TIMESTAMP(3),

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "customers" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "name" VARCHAR(200) NOT NULL,
    "tradeName" VARCHAR(200),
    "nif" VARCHAR(9),
    "cif" VARCHAR(9),
    "nie" VARCHAR(9),
    "vatNumber" VARCHAR(20),
    "address" VARCHAR(255) NOT NULL,
    "city" VARCHAR(100) NOT NULL,
    "state" VARCHAR(100),
    "postalCode" VARCHAR(10) NOT NULL,
    "country" VARCHAR(2) NOT NULL DEFAULT 'ES',
    "contactName" VARCHAR(100),
    "contactEmail" VARCHAR(255),
    "contactPhone" VARCHAR(20),
    "contactMobile" VARCHAR(20),
    "businessType" VARCHAR(50),
    "website" VARCHAR(255),
    "notes" VARCHAR(1000),
    "tags" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "externalWoocommerce" VARCHAR(50),
    "externalShopify" VARCHAR(50),
    "externalPrestashop" VARCHAR(50),
    "companyId" TEXT NOT NULL,
    "statsTotalInvoices" INTEGER NOT NULL DEFAULT 0,
    "statsTotalAmount" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "statsLastInvoiceDate" TIMESTAMP(3),
    "statsAveragePaymentDays" INTEGER NOT NULL DEFAULT 0,

    CONSTRAINT "customers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "invoices" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "invoiceNumber" VARCHAR(50) NOT NULL,
    "series" VARCHAR(5) NOT NULL DEFAULT 'A',
    "issueDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "dueDate" TIMESTAMP(3) NOT NULL,
    "serviceDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "customerId" TEXT NOT NULL,
    "companyId" TEXT NOT NULL,
    "items" JSONB NOT NULL DEFAULT '[]',
    "subtotal" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "taxAmount" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "total" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "totalPaid" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "totalDue" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "status" VARCHAR(20) NOT NULL DEFAULT 'draft',
    "paymentStatus" VARCHAR(20) NOT NULL DEFAULT 'pending',
    "paymentMethod" VARCHAR(50),
    "paymentReference" VARCHAR(100),
    "paymentDate" TIMESTAMP(3),
    "paymentNotes" VARCHAR(500),
    "notes" VARCHAR(1000),
    "internalReference" VARCHAR(100),
    "customerReference" VARCHAR(100),
    "externalWoocommerceOrderId" VARCHAR(50),
    "externalShopifyOrderId" VARCHAR(50),
    "externalPrestashopOrderId" VARCHAR(50),
    "sentDate" TIMESTAMP(3),
    "viewedDate" TIMESTAMP(3),
    "pdfGenerated" BOOLEAN NOT NULL DEFAULT false,
    "pdfPath" VARCHAR(500),
    "currency" VARCHAR(3) NOT NULL DEFAULT 'EUR',
    "exchangeRate" DECIMAL(10,4) NOT NULL DEFAULT 1,

    CONSTRAINT "invoices_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "invoice_items" (
    "id" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "description" VARCHAR(500) NOT NULL,
    "quantity" DECIMAL(10,3) NOT NULL,
    "unitPrice" DECIMAL(10,2) NOT NULL,
    "taxRate" DECIMAL(5,2) NOT NULL,
    "taxAmount" DECIMAL(10,2) NOT NULL,
    "total" DECIMAL(10,2) NOT NULL,
    "productCode" VARCHAR(50),
    "unit" VARCHAR(20),
    "invoiceId" TEXT NOT NULL,

    CONSTRAINT "invoice_items_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "companies_nif_key" ON "companies"("nif");

-- CreateIndex
CREATE UNIQUE INDEX "companies_cif_key" ON "companies"("cif");

-- CreateIndex
CREATE UNIQUE INDEX "companies_nie_key" ON "companies"("nie");

-- CreateIndex
CREATE UNIQUE INDEX "companies_vatNumber_key" ON "companies"("vatNumber");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "users_apiKey_key" ON "users"("apiKey");

-- CreateIndex
CREATE UNIQUE INDEX "invoices_invoiceNumber_key" ON "invoices"("invoiceNumber");

-- AddForeignKey
ALTER TABLE "users" ADD CONSTRAINT "users_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "customers" ADD CONSTRAINT "customers_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "customers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "companies"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "invoice_items" ADD CONSTRAINT "invoice_items_invoiceId_fkey" FOREIGN KEY ("invoiceId") REFERENCES "invoices"("id") ON DELETE CASCADE ON UPDATE CASCADE;
