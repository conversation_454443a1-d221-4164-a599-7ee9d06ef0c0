# Environment Configuration
NODE_ENV=development
PORT=3000

# Database Configuration
DATABASE_URL=mongodb://localhost:27017/veriapi
# For PostgreSQL: postgresql://username:password@localhost:5432/veriapi

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# API Configuration
API_BASE_URL=http://localhost:3000
API_VERSION=v1

# CORS Configuration
CORS_ORIGIN=http://localhost:3001,http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Spanish Localization
DEFAULT_TIMEZONE=Europe/Madrid
DEFAULT_CURRENCY=EUR
DEFAULT_LOCALE=es-ES

# Invoice Configuration
INVOICE_PREFIX=FAC
INVOICE_NUMBER_LENGTH=6
TAX_RATES=21,10,4,0

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/

# Security
BCRYPT_ROUNDS=12

# PDF Generation
PDF_TIMEOUT=30000
PDF_FORMAT=A4

# Company Default Information (can be overridden per user)
DEFAULT_COMPANY_NAME=Tu Empresa S.L.
DEFAULT_COMPANY_NIF=B12345678
DEFAULT_COMPANY_ADDRESS=Calle Principal 123
DEFAULT_COMPANY_CITY=Madrid
DEFAULT_COMPANY_POSTAL_CODE=28001
DEFAULT_COMPANY_COUNTRY=España
DEFAULT_COMPANY_PHONE=+34 91 123 45 67
DEFAULT_COMPANY_EMAIL=<EMAIL>
DEFAULT_COMPANY_WEBSITE=www.tuempresa.com
