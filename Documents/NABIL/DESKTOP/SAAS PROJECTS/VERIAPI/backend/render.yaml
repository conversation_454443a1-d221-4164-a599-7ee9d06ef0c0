services:
  - type: web
    name: veriapi-backend
    env: node
    plan: starter
    buildCommand: npm ci && npx prisma generate
    startCommand: npm run deploy
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: DATABASE_URL
        fromDatabase:
          name: veriapi-postgres
          property: connectionString
      - key: JWT_SECRET
        generateValue: true
      - key: API_BASE_URL
        value: https://api.soloun.link
      - key: FRONTEND_URL
        value: https://app.soloun.link
      - key: CORS_ORIGIN
        value: https://app.soloun.link,https://www.soloun.link

databases:
  - name: veriapi-postgres
    databaseName: veriapi
    user: veriapi
