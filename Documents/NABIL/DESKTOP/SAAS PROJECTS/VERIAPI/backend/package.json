{"name": "veriapi-backend", "version": "1.0.0", "description": "VeriAPI - Electronic Invoicing SaaS Backend API", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "build": "npx prisma generate", "deploy": "npx prisma migrate deploy && npm run build && npm start", "db:migrate": "npx prisma migrate dev", "db:deploy": "npx prisma migrate deploy", "db:generate": "npx prisma generate", "db:seed": "node src/scripts/seed.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["invoicing", "saas", "spanish", "api", "facturacion", "electronica"], "author": "VeriAPI Team", "license": "MIT", "dependencies": {"@prisma/client": "^6.11.1", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "handlebars": "^4.7.8", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "prisma": "^6.11.1", "puppeteer": "^21.6.1", "uuid": "^9.0.1"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}