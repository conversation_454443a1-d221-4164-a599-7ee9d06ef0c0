{"name": "veriapi-backend", "version": "1.0.0", "description": "VeriAPI - Electronic Invoicing SaaS Backend API", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["invoicing", "saas", "spanish", "api", "facturacion", "electronica"], "author": "VeriAPI Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "dotenv": "^16.3.1", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "puppeteer": "^21.6.1", "handlebars": "^4.7.8", "nodemailer": "^6.9.7", "uuid": "^9.0.1", "multer": "^1.4.5-lts.1", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1"}, "engines": {"node": ">=18.0.0"}}