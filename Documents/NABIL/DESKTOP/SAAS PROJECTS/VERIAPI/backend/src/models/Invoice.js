const mongoose = require('mongoose');
const moment = require('moment-timezone');

const invoiceLineSchema = new mongoose.Schema({
  description: {
    type: String,
    required: [true, 'La descripción del producto/servicio es obligatoria'],
    trim: true,
    maxlength: [500, 'La descripción no puede exceder 500 caracteres']
  },
  quantity: {
    type: Number,
    required: [true, 'La cantidad es obligatoria'],
    min: [0.01, 'La cantidad debe ser mayor a 0'],
    default: 1
  },
  unitPrice: {
    type: Number,
    required: [true, 'El precio unitario es obligatorio'],
    min: [0, 'El precio unitario no puede ser negativo']
  },
  discount: {
    type: Number,
    default: 0,
    min: [0, 'El descuento no puede ser negativo'],
    max: [100, 'El descuento no puede exceder 100%']
  },
  taxRate: {
    type: Number,
    required: [true, 'El tipo de IVA es obligatorio'],
    min: [0, 'El tipo de IVA no puede ser negativo'],
    max: [100, 'El tipo de IVA no puede exceder 100%'],
    default: 21
  },
  taxName: {
    type: String,
    default: 'IVA',
    trim: true
  },
  productCode: {
    type: String,
    trim: true,
    maxlength: [50, 'El código de producto no puede exceder 50 caracteres']
  },
  unit: {
    type: String,
    default: 'ud',
    trim: true,
    maxlength: [10, 'La unidad no puede exceder 10 caracteres']
  }
}, {
  _id: false,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual calculations for invoice line
invoiceLineSchema.virtual('subtotal').get(function() {
  return this.quantity * this.unitPrice;
});

invoiceLineSchema.virtual('discountAmount').get(function() {
  return (this.subtotal * this.discount) / 100;
});

invoiceLineSchema.virtual('netAmount').get(function() {
  return this.subtotal - this.discountAmount;
});

invoiceLineSchema.virtual('taxAmount').get(function() {
  return (this.netAmount * this.taxRate) / 100;
});

invoiceLineSchema.virtual('total').get(function() {
  return this.netAmount + this.taxAmount;
});

const invoiceSchema = new mongoose.Schema({
  // Invoice identification
  invoiceNumber: {
    type: String,
    required: [true, 'El número de factura es obligatorio'],
    unique: true,
    trim: true,
    uppercase: true
  },
  series: {
    type: String,
    default: 'A',
    trim: true,
    uppercase: true,
    maxlength: [5, 'La serie no puede exceder 5 caracteres']
  },
  // Dates
  issueDate: {
    type: Date,
    required: [true, 'La fecha de emisión es obligatoria'],
    default: Date.now
  },
  dueDate: {
    type: Date,
    required: [true, 'La fecha de vencimiento es obligatoria']
  },
  serviceDate: {
    type: Date,
    default: Date.now
  },
  // References
  customer: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Customer',
    required: [true, 'El cliente es obligatorio']
  },
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Company',
    required: [true, 'La empresa es obligatoria']
  },
  // Invoice lines
  lines: {
    type: [invoiceLineSchema],
    required: [true, 'La factura debe tener al menos una línea'],
    validate: {
      validator: function(lines) {
        return lines && lines.length > 0;
      },
      message: 'La factura debe tener al menos una línea'
    }
  },
  // Totals
  subtotal: {
    type: Number,
    default: 0,
    min: [0, 'El subtotal no puede ser negativo']
  },
  totalDiscount: {
    type: Number,
    default: 0,
    min: [0, 'El descuento total no puede ser negativo']
  },
  netAmount: {
    type: Number,
    default: 0,
    min: [0, 'El importe neto no puede ser negativo']
  },
  totalTax: {
    type: Number,
    default: 0,
    min: [0, 'El IVA total no puede ser negativo']
  },
  total: {
    type: Number,
    default: 0,
    min: [0, 'El total no puede ser negativo']
  },
  // Tax breakdown
  taxBreakdown: [{
    taxRate: {
      type: Number,
      required: true
    },
    taxName: {
      type: String,
      default: 'IVA'
    },
    taxableAmount: {
      type: Number,
      required: true
    },
    taxAmount: {
      type: Number,
      required: true
    }
  }],
  // Payment information
  paymentInfo: {
    method: {
      type: String,
      enum: ['transfer', 'card', 'cash', 'check', 'other'],
      default: 'transfer'
    },
    status: {
      type: String,
      enum: ['pending', 'partial', 'paid', 'overdue', 'cancelled'],
      default: 'pending'
    },
    paidAmount: {
      type: Number,
      default: 0,
      min: [0, 'El importe pagado no puede ser negativo']
    },
    paidDate: Date,
    reference: {
      type: String,
      trim: true,
      maxlength: [100, 'La referencia de pago no puede exceder 100 caracteres']
    }
  },
  // Spanish compliance fields
  compliance: {
    // IRPF (Personal Income Tax) retention
    irpfRate: {
      type: Number,
      default: 0,
      min: [0, 'El tipo de IRPF no puede ser negativo'],
      max: [100, 'El tipo de IRPF no puede exceder 100%']
    },
    irpfAmount: {
      type: Number,
      default: 0,
      min: [0, 'El importe de IRPF no puede ser negativo']
    },
    // Reverse charge for EU customers
    reverseCharge: {
      type: Boolean,
      default: false
    },
    // Special regime indicators
    specialRegime: {
      type: String,
      enum: ['general', 'simplified', 'agriculture', 'used_goods', 'travel_agencies', 'other'],
      default: 'general'
    },
    // SII (Immediate Supply of Information) status
    siiStatus: {
      type: String,
      enum: ['pending', 'sent', 'accepted', 'rejected'],
      default: 'pending'
    },
    siiReference: {
      type: String,
      trim: true
    }
  },
  // Additional information
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'Las notas no pueden exceder 1000 caracteres']
  },
  internalNotes: {
    type: String,
    trim: true,
    maxlength: [1000, 'Las notas internas no pueden exceder 1000 caracteres']
  },
  // External references
  externalReferences: {
    orderNumber: {
      type: String,
      trim: true,
      maxlength: [100, 'El número de pedido no puede exceder 100 caracteres']
    },
    woocommerceOrderId: {
      type: String,
      trim: true
    },
    shopifyOrderId: {
      type: String,
      trim: true
    },
    prestashopOrderId: {
      type: String,
      trim: true
    }
  },
  // Status and workflow
  status: {
    type: String,
    enum: ['draft', 'sent', 'viewed', 'paid', 'overdue', 'cancelled'],
    default: 'draft'
  },
  sentDate: Date,
  viewedDate: Date,
  // PDF generation
  pdfGenerated: {
    type: Boolean,
    default: false
  },
  pdfPath: {
    type: String,
    trim: true
  },
  // Currency (for future multi-currency support)
  currency: {
    type: String,
    default: 'EUR',
    uppercase: true,
    match: [/^[A-Z]{3}$/, 'Código de moneda no válido']
  },
  exchangeRate: {
    type: Number,
    default: 1,
    min: [0.01, 'El tipo de cambio debe ser mayor a 0']
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
invoiceSchema.index({ invoiceNumber: 1 });
invoiceSchema.index({ company: 1 });
invoiceSchema.index({ customer: 1 });
invoiceSchema.index({ issueDate: -1 });
invoiceSchema.index({ dueDate: 1 });
invoiceSchema.index({ status: 1 });
invoiceSchema.index({ 'paymentInfo.status': 1 });
invoiceSchema.index({ 'externalReferences.woocommerceOrderId': 1 });
invoiceSchema.index({ 'externalReferences.shopifyOrderId': 1 });
invoiceSchema.index({ 'externalReferences.prestashopOrderId': 1 });

// Virtual for formatted dates
invoiceSchema.virtual('formattedIssueDate').get(function() {
  return moment(this.issueDate).tz('Europe/Madrid').format('DD/MM/YYYY');
});

invoiceSchema.virtual('formattedDueDate').get(function() {
  return moment(this.dueDate).tz('Europe/Madrid').format('DD/MM/YYYY');
});

// Virtual for days until due
invoiceSchema.virtual('daysUntilDue').get(function() {
  return moment(this.dueDate).diff(moment(), 'days');
});

// Virtual for overdue status
invoiceSchema.virtual('isOverdue').get(function() {
  return this.paymentInfo.status !== 'paid' && moment().isAfter(this.dueDate);
});

// Virtual for remaining amount
invoiceSchema.virtual('remainingAmount').get(function() {
  return this.total - this.paymentInfo.paidAmount;
});

// Pre-save middleware to calculate totals
invoiceSchema.pre('save', function(next) {
  this.calculateTotals();
  next();
});

// Method to calculate totals
invoiceSchema.methods.calculateTotals = function() {
  let subtotal = 0;
  let totalDiscount = 0;
  let totalTax = 0;
  const taxBreakdown = new Map();

  // Calculate line totals
  this.lines.forEach(line => {
    const lineSubtotal = line.quantity * line.unitPrice;
    const lineDiscount = (lineSubtotal * line.discount) / 100;
    const lineNet = lineSubtotal - lineDiscount;
    const lineTax = (lineNet * line.taxRate) / 100;

    subtotal += lineSubtotal;
    totalDiscount += lineDiscount;
    totalTax += lineTax;

    // Group by tax rate for breakdown
    const taxKey = `${line.taxRate}-${line.taxName}`;
    if (taxBreakdown.has(taxKey)) {
      const existing = taxBreakdown.get(taxKey);
      existing.taxableAmount += lineNet;
      existing.taxAmount += lineTax;
    } else {
      taxBreakdown.set(taxKey, {
        taxRate: line.taxRate,
        taxName: line.taxName,
        taxableAmount: lineNet,
        taxAmount: lineTax
      });
    }
  });

  this.subtotal = Math.round(subtotal * 100) / 100;
  this.totalDiscount = Math.round(totalDiscount * 100) / 100;
  this.netAmount = Math.round((subtotal - totalDiscount) * 100) / 100;
  this.totalTax = Math.round(totalTax * 100) / 100;

  // Calculate IRPF if applicable
  if (this.compliance.irpfRate > 0) {
    this.compliance.irpfAmount = Math.round((this.netAmount * this.compliance.irpfRate / 100) * 100) / 100;
  }

  this.total = Math.round((this.netAmount + this.totalTax - (this.compliance.irpfAmount || 0)) * 100) / 100;

  // Update tax breakdown
  this.taxBreakdown = Array.from(taxBreakdown.values()).map(tax => ({
    taxRate: tax.taxRate,
    taxName: tax.taxName,
    taxableAmount: Math.round(tax.taxableAmount * 100) / 100,
    taxAmount: Math.round(tax.taxAmount * 100) / 100
  }));
};

// Method to mark as paid
invoiceSchema.methods.markAsPaid = function(amount, paymentDate, reference) {
  this.paymentInfo.paidAmount = amount || this.total;
  this.paymentInfo.paidDate = paymentDate || new Date();
  this.paymentInfo.reference = reference;
  this.paymentInfo.status = this.paymentInfo.paidAmount >= this.total ? 'paid' : 'partial';
  this.status = this.paymentInfo.status === 'paid' ? 'paid' : this.status;
  return this.save();
};

// Static method to generate next invoice number
invoiceSchema.statics.generateInvoiceNumber = async function(companyId) {
  const Company = mongoose.model('Company');
  const company = await Company.findById(companyId);
  if (!company) {
    throw new Error('Empresa no encontrada');
  }

  const invoiceNumber = company.getNextInvoiceNumber();
  await company.incrementInvoiceNumber();
  return invoiceNumber;
};

module.exports = mongoose.model('Invoice', invoiceSchema);
