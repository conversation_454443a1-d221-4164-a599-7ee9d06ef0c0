const mongoose = require('mongoose');

const customerSchema = new mongoose.Schema({
  // Basic information
  name: {
    type: String,
    required: [true, 'El nombre del cliente es obligatorio'],
    trim: true,
    maxlength: [200, 'El nombre no puede exceder 200 caracteres']
  },
  tradeName: {
    type: String,
    trim: true,
    maxlength: [200, 'El nombre comercial no puede exceder 200 caracteres']
  },
  customerType: {
    type: String,
    enum: ['individual', 'company'],
    required: [true, 'El tipo de cliente es obligatorio'],
    default: 'individual'
  },
  // Spanish tax identification
  nif: {
    type: String,
    trim: true,
    uppercase: true,
    match: [/^[0-9]{8}[TRWAGMYFPDXBNJZSQVHLCKE]$/i, 'NIF no válido']
  },
  cif: {
    type: String,
    trim: true,
    uppercase: true,
    match: [/^[ABCDEFGHJNPQRSUVW][0-9]{7}[0-9A-J]$/i, 'CIF no válido']
  },
  nie: {
    type: String,
    trim: true,
    uppercase: true,
    match: [/^[XYZ][0-9]{7}[TRWAGMYFPDXBNJZSQVHLCKE]$/i, 'NIE no válido']
  },
  vatNumber: {
    type: String,
    trim: true,
    uppercase: true,
    match: [/^[A-Z]{2}[A-Z0-9]{2,13}$/i, 'Número de IVA no válido']
  },
  // Address information
  address: {
    street: {
      type: String,
      required: [true, 'La dirección es obligatoria'],
      trim: true,
      maxlength: [200, 'La dirección no puede exceder 200 caracteres']
    },
    number: {
      type: String,
      trim: true,
      maxlength: [10, 'El número no puede exceder 10 caracteres']
    },
    floor: {
      type: String,
      trim: true,
      maxlength: [10, 'El piso no puede exceder 10 caracteres']
    },
    door: {
      type: String,
      trim: true,
      maxlength: [10, 'La puerta no puede exceder 10 caracteres']
    },
    city: {
      type: String,
      required: [true, 'La ciudad es obligatoria'],
      trim: true,
      maxlength: [100, 'La ciudad no puede exceder 100 caracteres']
    },
    province: {
      type: String,
      trim: true,
      maxlength: [100, 'La provincia no puede exceder 100 caracteres']
    },
    postalCode: {
      type: String,
      required: [true, 'El código postal es obligatorio'],
      trim: true,
      match: [/^[0-9]{5}$/, 'Código postal no válido (debe tener 5 dígitos)']
    },
    country: {
      type: String,
      default: 'España',
      trim: true,
      maxlength: [100, 'El país no puede exceder 100 caracteres']
    }
  },
  // Contact information
  contact: {
    phone: {
      type: String,
      trim: true,
      match: [/^(\+34|0034|34)?[6789][0-9]{8}$/, 'Número de teléfono español no válido']
    },
    mobile: {
      type: String,
      trim: true,
      match: [/^(\+34|0034|34)?[6789][0-9]{8}$/, 'Número de móvil español no válido']
    },
    email: {
      type: String,
      lowercase: true,
      trim: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Email no válido']
    },
    website: {
      type: String,
      trim: true,
      match: [/^https?:\/\/.+/, 'URL del sitio web no válida']
    }
  },
  // Tax information
  taxInfo: {
    isVatExempt: {
      type: Boolean,
      default: false
    },
    vatExemptReason: {
      type: String,
      trim: true
    },
    defaultTaxRate: {
      type: Number,
      default: 21,
      min: [0, 'El tipo de IVA no puede ser negativo'],
      max: [100, 'El tipo de IVA no puede exceder 100%']
    },
    // For EU customers
    isEuCustomer: {
      type: Boolean,
      default: false
    },
    // For reverse charge mechanism
    reverseCharge: {
      type: Boolean,
      default: false
    }
  },
  // Payment information
  paymentInfo: {
    paymentTerms: {
      type: Number,
      default: 30,
      min: [0, 'Los días de pago no pueden ser negativos']
    },
    preferredPaymentMethod: {
      type: String,
      enum: ['transfer', 'card', 'cash', 'check', 'other'],
      default: 'transfer'
    },
    bankAccount: {
      bankName: {
        type: String,
        trim: true,
        maxlength: [100, 'El nombre del banco no puede exceder 100 caracteres']
      },
      iban: {
        type: String,
        trim: true,
        uppercase: true,
        match: [/^[A-Z]{2}[0-9]{2}[A-Z0-9]{4}[0-9]{7}([A-Z0-9]?){0,16}$/, 'IBAN no válido']
      }
    },
    creditLimit: {
      type: Number,
      default: 0,
      min: [0, 'El límite de crédito no puede ser negativo']
    }
  },
  // Business relationship
  customerSince: {
    type: Date,
    default: Date.now
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'Las notas no pueden exceder 1000 caracteres']
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: [50, 'Cada etiqueta no puede exceder 50 caracteres']
  }],
  // Status
  isActive: {
    type: Boolean,
    default: true
  },
  // External integration IDs
  externalIds: {
    woocommerce: {
      type: String,
      trim: true
    },
    shopify: {
      type: String,
      trim: true
    },
    prestashop: {
      type: String,
      trim: true
    }
  },
  // Company reference
  company: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Company',
    required: true
  },
  // Statistics (calculated fields)
  stats: {
    totalInvoices: {
      type: Number,
      default: 0
    },
    totalAmount: {
      type: Number,
      default: 0
    },
    lastInvoiceDate: Date,
    averagePaymentDays: {
      type: Number,
      default: 0
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for full address
customerSchema.virtual('fullAddress').get(function() {
  const addr = this.address;
  let fullAddr = addr.street;
  if (addr.number) fullAddr += ` ${addr.number}`;
  if (addr.floor) fullAddr += `, ${addr.floor}`;
  if (addr.door) fullAddr += ` ${addr.door}`;
  fullAddr += `, ${addr.postalCode} ${addr.city}`;
  if (addr.province) fullAddr += `, ${addr.province}`;
  fullAddr += `, ${addr.country}`;
  return fullAddr;
});

// Virtual for tax identifier (prioritize CIF, then NIF, then NIE)
customerSchema.virtual('taxId').get(function() {
  return this.cif || this.nif || this.nie;
});

// Virtual for display name
customerSchema.virtual('displayName').get(function() {
  return this.tradeName || this.name;
});

// Index for performance
customerSchema.index({ company: 1 });
customerSchema.index({ cif: 1 });
customerSchema.index({ nif: 1 });
customerSchema.index({ nie: 1 });
customerSchema.index({ vatNumber: 1 });
customerSchema.index({ 'contact.email': 1 });
customerSchema.index({ name: 'text', tradeName: 'text' });
customerSchema.index({ 'externalIds.woocommerce': 1 });
customerSchema.index({ 'externalIds.shopify': 1 });
customerSchema.index({ 'externalIds.prestashop': 1 });

// Method to update statistics
customerSchema.methods.updateStats = async function() {
  const Invoice = mongoose.model('Invoice');
  
  const stats = await Invoice.aggregate([
    { $match: { customer: this._id } },
    {
      $group: {
        _id: null,
        totalInvoices: { $sum: 1 },
        totalAmount: { $sum: '$total' },
        lastInvoiceDate: { $max: '$issueDate' }
      }
    }
  ]);

  if (stats.length > 0) {
    this.stats.totalInvoices = stats[0].totalInvoices;
    this.stats.totalAmount = stats[0].totalAmount;
    this.stats.lastInvoiceDate = stats[0].lastInvoiceDate;
  }

  return this.save();
};

module.exports = mongoose.model('Customer', customerSchema);
