const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const { prisma } = require('../../config/database');

class UserModel {
  // Create a new user
  static async create(userData) {
    const { password, ...otherData } = userData;
    
    // Hash password
    const salt = await bcrypt.genSalt(parseInt(process.env.BCRYPT_ROUNDS) || 12);
    const hashedPassword = await bcrypt.hash(password, salt);

    return await prisma.user.create({
      data: {
        ...otherData,
        password: hashedPassword,
      },
      include: {
        company: true,
      },
    });
  }

  // Find user by email
  static async findByEmail(email) {
    return await prisma.user.findUnique({
      where: { email: email.toLowerCase() },
      include: {
        company: true,
      },
    });
  }

  // Find user by ID
  static async findById(id) {
    return await prisma.user.findUnique({
      where: { id },
      include: {
        company: true,
      },
    });
  }

  // Find user by API key
  static async findByApi<PERSON>ey(apiKey) {
    return await prisma.user.findUnique({
      where: { apiKey },
      include: {
        company: true,
      },
    });
  }

  // Update user
  static async update(id, updateData) {
    const { password, ...otherData } = updateData;
    
    let data = { ...otherData };
    
    // Hash password if provided
    if (password) {
      const salt = await bcrypt.genSalt(parseInt(process.env.BCRYPT_ROUNDS) || 12);
      data.password = await bcrypt.hash(password, salt);
    }

    return await prisma.user.update({
      where: { id },
      data,
      include: {
        company: true,
      },
    });
  }

  // Delete user
  static async delete(id) {
    return await prisma.user.delete({
      where: { id },
    });
  }

  // Compare password
  static async comparePassword(user, candidatePassword) {
    return bcrypt.compare(candidatePassword, user.password);
  }

  // Generate API key
  static generateApiKey() {
    return `vapi_${crypto.randomBytes(32).toString('hex')}`;
  }

  // Update last login
  static async updateLastLogin(id) {
    return await prisma.user.update({
      where: { id },
      data: { lastLogin: new Date() },
    });
  }

  // Find users by company
  static async findByCompany(companyId, options = {}) {
    const { page = 1, limit = 10, search } = options;
    const skip = (page - 1) * limit;

    const where = {
      companyId,
      ...(search && {
        OR: [
          { firstName: { contains: search, mode: 'insensitive' } },
          { lastName: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
        ],
      }),
    };

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: limit,
        include: {
          company: true,
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.user.count({ where }),
    ]);

    return {
      users,
      total,
      page,
      pages: Math.ceil(total / limit),
    };
  }

  // Set password reset token
  static async setPasswordResetToken(email) {
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    await prisma.user.update({
      where: { email: email.toLowerCase() },
      data: {
        resetPasswordToken: resetToken,
        resetPasswordExpires: resetTokenExpires,
      },
    });

    return resetToken;
  }

  // Reset password
  static async resetPassword(token, newPassword) {
    const user = await prisma.user.findFirst({
      where: {
        resetPasswordToken: token,
        resetPasswordExpires: { gt: new Date() },
      },
    });

    if (!user) {
      throw new Error('Token de restablecimiento inválido o expirado');
    }

    const salt = await bcrypt.genSalt(parseInt(process.env.BCRYPT_ROUNDS) || 12);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    return await prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        resetPasswordToken: null,
        resetPasswordExpires: null,
      },
    });
  }

  // Verify email
  static async verifyEmail(token) {
    const user = await prisma.user.findFirst({
      where: { emailVerificationToken: token },
    });

    if (!user) {
      throw new Error('Token de verificación inválido');
    }

    return await prisma.user.update({
      where: { id: user.id },
      data: {
        emailVerified: true,
        emailVerifiedAt: new Date(),
        emailVerificationToken: null,
      },
    });
  }

  // Remove sensitive fields from user object
  static sanitizeUser(user) {
    if (!user) return null;
    
    const { password, resetPasswordToken, resetPasswordExpires, emailVerificationToken, ...sanitizedUser } = user;
    
    // Add virtual fullName field
    sanitizedUser.fullName = `${user.firstName} ${user.lastName}`;
    
    return sanitizedUser;
  }
}

module.exports = UserModel;
