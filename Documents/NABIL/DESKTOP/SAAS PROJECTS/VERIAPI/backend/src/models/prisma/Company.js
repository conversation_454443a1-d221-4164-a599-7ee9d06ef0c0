const { prisma } = require('../../config/database');

class CompanyModel {
  // Create a new company
  static async create(companyData) {
    // Set default tax rates if not provided
    if (!companyData.taxRates || companyData.taxRates.length === 0) {
      companyData.taxRates = [
        { name: 'IVA General', rate: 21, description: 'Tipo general del IVA' },
        { name: 'IVA Reducido', rate: 10, description: 'Tipo reducido del IVA' },
        { name: 'IVA Superreducido', rate: 4, description: 'Tipo superreducido del IVA' },
        { name: 'Exento', rate: 0, description: 'Exento de IVA' }
      ];
    }

    return await prisma.company.create({
      data: companyData,
      include: {
        users: true,
        customers: true,
        _count: {
          select: {
            users: true,
            customers: true,
            invoices: true,
          },
        },
      },
    });
  }

  // Find company by ID
  static async findById(id) {
    return await prisma.company.findUnique({
      where: { id },
      include: {
        users: true,
        customers: true,
        _count: {
          select: {
            users: true,
            customers: true,
            invoices: true,
          },
        },
      },
    });
  }

  // Find company by tax ID (CIF, NIF, NIE)
  static async findByTaxId(taxId) {
    return await prisma.company.findFirst({
      where: {
        OR: [
          { cif: taxId },
          { nif: taxId },
          { nie: taxId },
          { vatNumber: taxId },
        ],
      },
    });
  }

  // Update company
  static async update(id, updateData) {
    return await prisma.company.update({
      where: { id },
      data: updateData,
      include: {
        users: true,
        customers: true,
        _count: {
          select: {
            users: true,
            customers: true,
            invoices: true,
          },
        },
      },
    });
  }

  // Delete company
  static async delete(id) {
    return await prisma.company.delete({
      where: { id },
    });
  }

  // Get all companies with pagination
  static async findAll(options = {}) {
    const { page = 1, limit = 10, search, isActive } = options;
    const skip = (page - 1) * limit;

    const where = {
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { tradeName: { contains: search, mode: 'insensitive' } },
          { contactEmail: { contains: search, mode: 'insensitive' } },
        ],
      }),
      ...(isActive !== undefined && { isActive }),
    };

    const [companies, total] = await Promise.all([
      prisma.company.findMany({
        where,
        skip,
        take: limit,
        include: {
          _count: {
            select: {
              users: true,
              customers: true,
              invoices: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.company.count({ where }),
    ]);

    return {
      companies,
      total,
      page,
      pages: Math.ceil(total / limit),
    };
  }

  // Get next invoice number
  static async getNextInvoiceNumber(id) {
    const company = await prisma.company.findUnique({
      where: { id },
      select: { nextInvoiceNumber: true, invoicePrefix: true, invoiceNumberLength: true },
    });

    if (!company) {
      throw new Error('Empresa no encontrada');
    }

    const nextNumber = company.nextInvoiceNumber;
    const paddedNumber = nextNumber.toString().padStart(company.invoiceNumberLength, '0');
    const invoiceNumber = `${company.invoicePrefix}${paddedNumber}`;

    // Increment the next invoice number
    await prisma.company.update({
      where: { id },
      data: { nextInvoiceNumber: nextNumber + 1 },
    });

    return invoiceNumber;
  }

  // Get company statistics
  static async getStatistics(id) {
    const [
      totalCustomers,
      totalInvoices,
      totalRevenue,
      pendingInvoices,
      overdueInvoices,
    ] = await Promise.all([
      prisma.customer.count({ where: { companyId: id, isActive: true } }),
      prisma.invoice.count({ where: { companyId: id } }),
      prisma.invoice.aggregate({
        where: { companyId: id, status: { not: 'cancelled' } },
        _sum: { total: true },
      }),
      prisma.invoice.count({
        where: { companyId: id, paymentStatus: 'pending' },
      }),
      prisma.invoice.count({
        where: {
          companyId: id,
          paymentStatus: 'pending',
          dueDate: { lt: new Date() },
        },
      }),
    ]);

    return {
      totalCustomers,
      totalInvoices,
      totalRevenue: totalRevenue._sum.total || 0,
      pendingInvoices,
      overdueInvoices,
    };
  }

  // Get monthly revenue data
  static async getMonthlyRevenue(id, year = new Date().getFullYear()) {
    const startDate = new Date(year, 0, 1);
    const endDate = new Date(year + 1, 0, 1);

    const monthlyData = await prisma.invoice.groupBy({
      by: ['issueDate'],
      where: {
        companyId: id,
        status: { not: 'cancelled' },
        issueDate: {
          gte: startDate,
          lt: endDate,
        },
      },
      _sum: {
        total: true,
      },
    });

    // Process data to get monthly totals
    const monthlyRevenue = Array(12).fill(0);
    
    monthlyData.forEach(item => {
      const month = new Date(item.issueDate).getMonth();
      monthlyRevenue[month] += Number(item._sum.total || 0);
    });

    return monthlyRevenue;
  }

  // Validate Spanish tax ID
  static validateSpanishTaxId(type, value) {
    const patterns = {
      nif: /^[0-9]{8}[TRWAGMYFPDXBNJZSQVHLCKE]$/i,
      cif: /^[ABCDEFGHJNPQRSUVW][0-9]{7}[0-9A-J]$/i,
      nie: /^[XYZ][0-9]{7}[TRWAGMYFPDXBNJZSQVHLCKE]$/i,
    };

    return patterns[type] ? patterns[type].test(value) : false;
  }

  // Get display name (tradeName or name)
  static getDisplayName(company) {
    return company.tradeName || company.name;
  }
}

module.exports = CompanyModel;
