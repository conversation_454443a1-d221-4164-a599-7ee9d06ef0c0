const mongoose = require('mongoose');

const companySchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'El nombre de la empresa es obligatorio'],
    trim: true,
    maxlength: [200, 'El nombre no puede exceder 200 caracteres']
  },
  tradeName: {
    type: String,
    trim: true,
    maxlength: [200, 'El nombre comercial no puede exceder 200 caracteres']
  },
  // Spanish tax identification
  nif: {
    type: String,
    trim: true,
    uppercase: true,
    match: [/^[0-9]{8}[TRWAGMYFPDXBNJZSQVHLCKE]$/i, 'NIF no válido']
  },
  cif: {
    type: String,
    trim: true,
    uppercase: true,
    match: [/^[ABCDEFGHJNPQRSUVW][0-9]{7}[0-9A-J]$/i, 'CIF no válido']
  },
  nie: {
    type: String,
    trim: true,
    uppercase: true,
    match: [/^[XYZ][0-9]{7}[TRWAGMYFPDXBNJZSQVHLCKE]$/i, 'NIE no válido']
  },
  vatNumber: {
    type: String,
    trim: true,
    uppercase: true,
    match: [/^ES[A-Z0-9]{9}$/i, 'Número de IVA no válido']
  },
  // Address information
  address: {
    street: {
      type: String,
      required: [true, 'La dirección es obligatoria'],
      trim: true,
      maxlength: [200, 'La dirección no puede exceder 200 caracteres']
    },
    number: {
      type: String,
      trim: true,
      maxlength: [10, 'El número no puede exceder 10 caracteres']
    },
    floor: {
      type: String,
      trim: true,
      maxlength: [10, 'El piso no puede exceder 10 caracteres']
    },
    door: {
      type: String,
      trim: true,
      maxlength: [10, 'La puerta no puede exceder 10 caracteres']
    },
    city: {
      type: String,
      required: [true, 'La ciudad es obligatoria'],
      trim: true,
      maxlength: [100, 'La ciudad no puede exceder 100 caracteres']
    },
    province: {
      type: String,
      trim: true,
      maxlength: [100, 'La provincia no puede exceder 100 caracteres']
    },
    postalCode: {
      type: String,
      required: [true, 'El código postal es obligatorio'],
      trim: true,
      match: [/^[0-9]{5}$/, 'Código postal no válido (debe tener 5 dígitos)']
    },
    country: {
      type: String,
      default: 'España',
      trim: true,
      maxlength: [100, 'El país no puede exceder 100 caracteres']
    }
  },
  // Contact information
  contact: {
    phone: {
      type: String,
      trim: true,
      match: [/^(\+34|0034|34)?[6789][0-9]{8}$/, 'Número de teléfono español no válido']
    },
    mobile: {
      type: String,
      trim: true,
      match: [/^(\+34|0034|34)?[6789][0-9]{8}$/, 'Número de móvil español no válido']
    },
    email: {
      type: String,
      required: [true, 'El email es obligatorio'],
      lowercase: true,
      trim: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Email no válido']
    },
    website: {
      type: String,
      trim: true,
      match: [/^https?:\/\/.+/, 'URL del sitio web no válida']
    }
  },
  // Business information
  businessType: {
    type: String,
    enum: [
      'autonomo', 'sociedad_limitada', 'sociedad_anonima', 
      'sociedad_cooperativa', 'comunidad_bienes', 'otro'
    ],
    default: 'autonomo'
  },
  sector: {
    type: String,
    trim: true,
    maxlength: [100, 'El sector no puede exceder 100 caracteres']
  },
  // Tax configuration
  taxSettings: {
    defaultTaxRate: {
      type: Number,
      default: 21,
      min: [0, 'El tipo de IVA no puede ser negativo'],
      max: [100, 'El tipo de IVA no puede exceder 100%']
    },
    taxRates: [{
      name: {
        type: String,
        required: true,
        trim: true
      },
      rate: {
        type: Number,
        required: true,
        min: 0,
        max: 100
      },
      description: {
        type: String,
        trim: true
      }
    }],
    isVatExempt: {
      type: Boolean,
      default: false
    },
    vatExemptReason: {
      type: String,
      trim: true
    },
    // IRPF (Personal Income Tax) for freelancers
    irpfRate: {
      type: Number,
      default: 0,
      min: [0, 'El tipo de IRPF no puede ser negativo'],
      max: [100, 'El tipo de IRPF no puede exceder 100%']
    }
  },
  // Invoice configuration
  invoiceSettings: {
    prefix: {
      type: String,
      default: 'FAC',
      trim: true,
      uppercase: true,
      maxlength: [10, 'El prefijo no puede exceder 10 caracteres']
    },
    nextNumber: {
      type: Number,
      default: 1,
      min: [1, 'El número de factura debe ser mayor a 0']
    },
    numberLength: {
      type: Number,
      default: 6,
      min: [1, 'La longitud mínima es 1'],
      max: [10, 'La longitud máxima es 10']
    },
    dueDays: {
      type: Number,
      default: 30,
      min: [0, 'Los días de vencimiento no pueden ser negativos']
    }
  },
  // Bank information
  bankInfo: {
    bankName: {
      type: String,
      trim: true,
      maxlength: [100, 'El nombre del banco no puede exceder 100 caracteres']
    },
    iban: {
      type: String,
      trim: true,
      uppercase: true,
      match: [/^ES[0-9]{22}$/, 'IBAN español no válido']
    },
    swift: {
      type: String,
      trim: true,
      uppercase: true,
      match: [/^[A-Z]{6}[A-Z0-9]{2}([A-Z0-9]{3})?$/, 'Código SWIFT no válido']
    }
  },
  // Logo and branding
  logo: {
    type: String, // URL or base64
    trim: true
  },
  // Status
  isActive: {
    type: Boolean,
    default: true
  },
  // Subscription info (for future use)
  subscription: {
    plan: {
      type: String,
      enum: ['free', 'basic', 'premium', 'enterprise'],
      default: 'free'
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'suspended', 'cancelled'],
      default: 'active'
    },
    expiresAt: Date,
    maxInvoicesPerMonth: {
      type: Number,
      default: 10
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Virtual for full address
companySchema.virtual('fullAddress').get(function() {
  const addr = this.address;
  let fullAddr = addr.street;
  if (addr.number) fullAddr += ` ${addr.number}`;
  if (addr.floor) fullAddr += `, ${addr.floor}`;
  if (addr.door) fullAddr += ` ${addr.door}`;
  fullAddr += `, ${addr.postalCode} ${addr.city}`;
  if (addr.province) fullAddr += `, ${addr.province}`;
  fullAddr += `, ${addr.country}`;
  return fullAddr;
});

// Virtual for tax identifier (prioritize CIF, then NIF, then NIE)
companySchema.virtual('taxId').get(function() {
  return this.cif || this.nif || this.nie;
});

// Index for performance
companySchema.index({ cif: 1 });
companySchema.index({ nif: 1 });
companySchema.index({ nie: 1 });
companySchema.index({ vatNumber: 1 });
companySchema.index({ 'contact.email': 1 });

// Pre-save middleware to set default tax rates
companySchema.pre('save', function(next) {
  if (this.isNew && (!this.taxSettings.taxRates || this.taxSettings.taxRates.length === 0)) {
    this.taxSettings.taxRates = [
      { name: 'IVA General', rate: 21, description: 'Tipo general del IVA' },
      { name: 'IVA Reducido', rate: 10, description: 'Tipo reducido del IVA' },
      { name: 'IVA Superreducido', rate: 4, description: 'Tipo superreducido del IVA' },
      { name: 'Exento', rate: 0, description: 'Exento de IVA' }
    ];
  }
  next();
});

// Method to get next invoice number
companySchema.methods.getNextInvoiceNumber = function() {
  const settings = this.invoiceSettings;
  const number = settings.nextNumber.toString().padStart(settings.numberLength, '0');
  return `${settings.prefix}${number}`;
};

// Method to increment invoice number
companySchema.methods.incrementInvoiceNumber = function() {
  this.invoiceSettings.nextNumber += 1;
  return this.save();
};

module.exports = mongoose.model('Company', companySchema);
