const { prisma } = require('../../config/database');
const moment = require('moment-timezone');

class InvoiceModel {
  // Create a new invoice
  static async create(invoiceData) {
    const { items, ...invoiceFields } = invoiceData;

    // Calculate totals
    const totals = this.calculateTotals(items || []);

    return await prisma.invoice.create({
      data: {
        ...invoiceFields,
        items: items || [],
        subtotal: totals.subtotal,
        taxAmount: totals.taxAmount,
        total: totals.total,
        totalDue: totals.total,
      },
      include: {
        customer: true,
        company: true,
      },
    });
  }

  // Find invoice by ID
  static async findById(id) {
    return await prisma.invoice.findUnique({
      where: { id },
      include: {
        customer: true,
        company: true,
      },
    });
  }

  // Find invoice by invoice number
  static async findByInvoiceNumber(invoiceNumber) {
    return await prisma.invoice.findUnique({
      where: { invoiceNumber },
      include: {
        customer: true,
        company: true,
      },
    });
  }

  // Find invoices by company
  static async findByCompany(companyId, options = {}) {
    const { 
      page = 1, 
      limit = 10, 
      search, 
      status, 
      paymentStatus,
      customerId,
      dateFrom,
      dateTo,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = options;
    
    const skip = (page - 1) * limit;

    const where = {
      companyId,
      ...(search && {
        OR: [
          { invoiceNumber: { contains: search, mode: 'insensitive' } },
          { customer: { name: { contains: search, mode: 'insensitive' } } },
          { customer: { tradeName: { contains: search, mode: 'insensitive' } } },
        ],
      }),
      ...(status && { status }),
      ...(paymentStatus && { paymentStatus }),
      ...(customerId && { customerId }),
      ...(dateFrom && { issueDate: { gte: new Date(dateFrom) } }),
      ...(dateTo && { issueDate: { lte: new Date(dateTo) } }),
    };

    const [invoices, total] = await Promise.all([
      prisma.invoice.findMany({
        where,
        skip,
        take: limit,
        include: {
          customer: true,
          company: true,
        },
        orderBy: { [sortBy]: sortOrder },
      }),
      prisma.invoice.count({ where }),
    ]);

    return {
      invoices,
      total,
      page,
      pages: Math.ceil(total / limit),
    };
  }

  // Update invoice
  static async update(id, updateData) {
    const { items, ...otherFields } = updateData;

    let data = { ...otherFields };

    // Recalculate totals if items are updated
    if (items) {
      const totals = this.calculateTotals(items);
      data = {
        ...data,
        items,
        subtotal: totals.subtotal,
        taxAmount: totals.taxAmount,
        total: totals.total,
        totalDue: totals.total - (otherFields.totalPaid || 0),
      };
    }

    return await prisma.invoice.update({
      where: { id },
      data,
      include: {
        customer: true,
        company: true,
      },
    });
  }

  // Delete invoice
  static async delete(id) {
    return await prisma.invoice.delete({
      where: { id },
    });
  }

  // Mark invoice as sent
  static async markAsSent(id) {
    return await prisma.invoice.update({
      where: { id },
      data: {
        status: 'sent',
        sentDate: new Date(),
      },
    });
  }

  // Mark invoice as viewed
  static async markAsViewed(id) {
    return await prisma.invoice.update({
      where: { id },
      data: { viewedDate: new Date() },
    });
  }

  // Record payment
  static async recordPayment(id, paymentData) {
    const { amount, method, reference, notes } = paymentData;
    
    const invoice = await this.findById(id);
    if (!invoice) {
      throw new Error('Factura no encontrada');
    }

    const newTotalPaid = Number(invoice.totalPaid) + Number(amount);
    const newTotalDue = Number(invoice.total) - newTotalPaid;
    
    const paymentStatus = newTotalDue <= 0 ? 'paid' : 'partial';

    return await prisma.invoice.update({
      where: { id },
      data: {
        totalPaid: newTotalPaid,
        totalDue: newTotalDue,
        paymentStatus,
        paymentMethod: method,
        paymentReference: reference,
        paymentNotes: notes,
        paymentDate: paymentStatus === 'paid' ? new Date() : invoice.paymentDate,
      },
      include: {
        customer: true,
        company: true,
      },
    });
  }

  // Calculate invoice totals
  static calculateTotals(items) {
    let subtotal = 0;
    let taxAmount = 0;

    items.forEach(item => {
      const itemTotal = Number(item.quantity) * Number(item.unitPrice);
      const itemTax = itemTotal * (Number(item.taxRate) / 100);
      
      subtotal += itemTotal;
      taxAmount += itemTax;
    });

    return {
      subtotal: Number(subtotal.toFixed(2)),
      taxAmount: Number(taxAmount.toFixed(2)),
      total: Number((subtotal + taxAmount).toFixed(2)),
    };
  }

  // Get invoice statistics for a company
  static async getCompanyStatistics(companyId, period = 'month') {
    const now = new Date();
    let startDate;

    switch (period) {
      case 'week':
        startDate = moment().startOf('week').toDate();
        break;
      case 'month':
        startDate = moment().startOf('month').toDate();
        break;
      case 'year':
        startDate = moment().startOf('year').toDate();
        break;
      default:
        startDate = moment().startOf('month').toDate();
    }

    const [
      totalInvoices,
      totalRevenue,
      paidInvoices,
      pendingInvoices,
      overdueInvoices,
    ] = await Promise.all([
      prisma.invoice.count({
        where: {
          companyId,
          issueDate: { gte: startDate },
        },
      }),
      prisma.invoice.aggregate({
        where: {
          companyId,
          issueDate: { gte: startDate },
          status: { not: 'cancelled' },
        },
        _sum: { total: true },
      }),
      prisma.invoice.count({
        where: {
          companyId,
          paymentStatus: 'paid',
          issueDate: { gte: startDate },
        },
      }),
      prisma.invoice.count({
        where: {
          companyId,
          paymentStatus: 'pending',
        },
      }),
      prisma.invoice.count({
        where: {
          companyId,
          paymentStatus: 'pending',
          dueDate: { lt: now },
        },
      }),
    ]);

    return {
      totalInvoices,
      totalRevenue: totalRevenue._sum.total || 0,
      paidInvoices,
      pendingInvoices,
      overdueInvoices,
      period,
    };
  }

  // Get formatted dates
  static getFormattedDates(invoice) {
    const timezone = 'Europe/Madrid';
    
    return {
      formattedIssueDate: moment(invoice.issueDate).tz(timezone).format('DD/MM/YYYY'),
      formattedDueDate: moment(invoice.dueDate).tz(timezone).format('DD/MM/YYYY'),
      formattedServiceDate: moment(invoice.serviceDate).tz(timezone).format('DD/MM/YYYY'),
    };
  }

  // Check if invoice is overdue
  static isOverdue(invoice) {
    return invoice.paymentStatus === 'pending' && new Date(invoice.dueDate) < new Date();
  }

  // Get next due date based on company settings
  static getNextDueDate(company, issueDate = new Date()) {
    const dueDays = company.dueDays || 30;
    return moment(issueDate).add(dueDays, 'days').toDate();
  }
}

module.exports = InvoiceModel;
