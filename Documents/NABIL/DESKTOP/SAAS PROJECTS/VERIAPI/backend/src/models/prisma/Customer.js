const { prisma } = require('../../config/database');

class CustomerModel {
  // Create a new customer
  static async create(customerData) {
    return await prisma.customer.create({
      data: customerData,
      include: {
        company: true,
        _count: {
          select: {
            invoices: true,
          },
        },
      },
    });
  }

  // Find customer by ID
  static async findById(id) {
    return await prisma.customer.findUnique({
      where: { id },
      include: {
        company: true,
        invoices: {
          orderBy: { createdAt: 'desc' },
          take: 5, // Last 5 invoices
        },
        _count: {
          select: {
            invoices: true,
          },
        },
      },
    });
  }

  // Find customers by company
  static async findByCompany(companyId, options = {}) {
    const { page = 1, limit = 10, search, isActive, tags } = options;
    const skip = (page - 1) * limit;

    const where = {
      companyId,
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { tradeName: { contains: search, mode: 'insensitive' } },
          { contactEmail: { contains: search, mode: 'insensitive' } },
          { contactName: { contains: search, mode: 'insensitive' } },
        ],
      }),
      ...(isActive !== undefined && { isActive }),
      ...(tags && tags.length > 0 && {
        tags: {
          hasSome: tags,
        },
      }),
    };

    const [customers, total] = await Promise.all([
      prisma.customer.findMany({
        where,
        skip,
        take: limit,
        include: {
          _count: {
            select: {
              invoices: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.customer.count({ where }),
    ]);

    return {
      customers,
      total,
      page,
      pages: Math.ceil(total / limit),
    };
  }

  // Find customer by tax ID
  static async findByTaxId(companyId, taxId) {
    return await prisma.customer.findFirst({
      where: {
        companyId,
        OR: [
          { cif: taxId },
          { nif: taxId },
          { nie: taxId },
          { vatNumber: taxId },
        ],
      },
    });
  }

  // Find customer by external ID
  static async findByExternalId(companyId, platform, externalId) {
    const externalField = `external${platform.charAt(0).toUpperCase() + platform.slice(1)}`;
    
    return await prisma.customer.findFirst({
      where: {
        companyId,
        [externalField]: externalId,
      },
    });
  }

  // Update customer
  static async update(id, updateData) {
    return await prisma.customer.update({
      where: { id },
      data: updateData,
      include: {
        company: true,
        _count: {
          select: {
            invoices: true,
          },
        },
      },
    });
  }

  // Delete customer
  static async delete(id) {
    // Check if customer has invoices
    const invoiceCount = await prisma.invoice.count({
      where: { customerId: id },
    });

    if (invoiceCount > 0) {
      throw new Error('No se puede eliminar un cliente que tiene facturas asociadas');
    }

    return await prisma.customer.delete({
      where: { id },
    });
  }

  // Update customer statistics
  static async updateStatistics(customerId) {
    const stats = await prisma.invoice.aggregate({
      where: {
        customerId,
        status: { not: 'cancelled' },
      },
      _count: true,
      _sum: {
        total: true,
      },
    });

    const lastInvoice = await prisma.invoice.findFirst({
      where: { customerId },
      orderBy: { issueDate: 'desc' },
      select: { issueDate: true },
    });

    // Calculate average payment days
    const paidInvoices = await prisma.invoice.findMany({
      where: {
        customerId,
        paymentStatus: 'paid',
        paymentDate: { not: null },
      },
      select: {
        issueDate: true,
        paymentDate: true,
      },
    });

    let averagePaymentDays = 0;
    if (paidInvoices.length > 0) {
      const totalDays = paidInvoices.reduce((sum, invoice) => {
        const days = Math.ceil(
          (new Date(invoice.paymentDate) - new Date(invoice.issueDate)) / (1000 * 60 * 60 * 24)
        );
        return sum + days;
      }, 0);
      averagePaymentDays = Math.round(totalDays / paidInvoices.length);
    }

    return await prisma.customer.update({
      where: { id: customerId },
      data: {
        statsTotalInvoices: stats._count || 0,
        statsTotalAmount: stats._sum.total || 0,
        statsLastInvoiceDate: lastInvoice?.issueDate || null,
        statsAveragePaymentDays: averagePaymentDays,
      },
    });
  }

  // Get customer statistics
  static async getStatistics(customerId) {
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      select: {
        statsTotalInvoices: true,
        statsTotalAmount: true,
        statsLastInvoiceDate: true,
        statsAveragePaymentDays: true,
      },
    });

    const [pendingAmount, overdueAmount] = await Promise.all([
      prisma.invoice.aggregate({
        where: {
          customerId,
          paymentStatus: 'pending',
        },
        _sum: { totalDue: true },
      }),
      prisma.invoice.aggregate({
        where: {
          customerId,
          paymentStatus: 'pending',
          dueDate: { lt: new Date() },
        },
        _sum: { totalDue: true },
      }),
    ]);

    return {
      ...customer,
      pendingAmount: pendingAmount._sum.totalDue || 0,
      overdueAmount: overdueAmount._sum.totalDue || 0,
    };
  }

  // Get all unique tags
  static async getAllTags(companyId) {
    const customers = await prisma.customer.findMany({
      where: { companyId },
      select: { tags: true },
    });

    const allTags = customers.flatMap(customer => customer.tags);
    return [...new Set(allTags)].sort();
  }

  // Get display name (tradeName or name)
  static getDisplayName(customer) {
    return customer.tradeName || customer.name;
  }

  // Validate Spanish tax ID
  static validateSpanishTaxId(type, value) {
    const patterns = {
      nif: /^[0-9]{8}[TRWAGMYFPDXBNJZSQVHLCKE]$/i,
      cif: /^[ABCDEFGHJNPQRSUVW][0-9]{7}[0-9A-J]$/i,
      nie: /^[XYZ][0-9]{7}[TRWAGMYFPDXBNJZSQVHLCKE]$/i,
    };

    return patterns[type] ? patterns[type].test(value) : false;
  }
}

module.exports = CustomerModel;
