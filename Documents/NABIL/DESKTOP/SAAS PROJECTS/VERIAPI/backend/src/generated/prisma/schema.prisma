// VeriAPI Prisma Schema - PostgreSQL Migration
// Migrated from MongoDB to PostgreSQL with Neon.tech

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Company Model
model Company {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Basic information
  name      String  @db.VarChar(200)
  tradeName String? @db.VarChar(200)

  // Spanish tax identification
  nif String? @unique @db.VarChar(9)
  cif String? @unique @db.VarChar(9)
  nie String? @unique @db.VarChar(9)

  // EU VAT number
  vatNumber String? @unique @db.VarChar(20)

  // Address
  address    String  @db.VarChar(255)
  city       String  @db.VarChar(100)
  state      String? @db.VarChar(100)
  postalCode String  @db.VarChar(10)
  country    String  @default("ES") @db.VarChar(2)

  // Contact information
  contactPhone  String? @db.VarChar(20)
  contactMobile String? @db.VarChar(20)
  contactEmail  String  @db.VarChar(255)
  website       String? @db.VarChar(255)

  // Business information
  businessType String  @default("autonomo") @db.VarChar(50)
  sector       String? @db.VarChar(100)
  employees    Int?
  foundedYear  Int?

  // Tax settings
  taxRates Json @default("[]")

  // Invoice settings
  invoicePrefix       String @default("FAC") @db.VarChar(10)
  invoiceNumberLength Int    @default(6)
  nextInvoiceNumber   Int    @default(1)
  dueDays             Int    @default(30)

  // Bank information
  bankName String? @db.VarChar(100)
  iban     String? @db.VarChar(34)
  swift    String? @db.VarChar(11)

  // Logo and branding
  logo String? @db.Text

  // Status
  isActive Boolean @default(true)

  // Subscription info
  subscriptionPlan                String    @default("free") @db.VarChar(20)
  subscriptionStatus              String    @default("active") @db.VarChar(20)
  subscriptionExpiresAt           DateTime?
  subscriptionMaxInvoicesPerMonth Int       @default(10)

  // Relations
  users     User[]
  customers Customer[]
  invoices  Invoice[]

  @@map("companies")
}

// User Model
model User {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Basic information
  email     String @unique @db.VarChar(255)
  password  String @db.VarChar(255)
  firstName String @db.VarChar(50)
  lastName  String @db.VarChar(50)

  // Role and status
  role      String    @default("user") @db.VarChar(20)
  isActive  Boolean   @default(true)
  lastLogin DateTime?

  // API access
  apiKey String? @unique @db.VarChar(100)

  // Company relation
  companyId String
  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)

  // Preferences
  preferenceLanguage   String  @default("es") @db.VarChar(5)
  preferenceTimezone   String  @default("Europe/Madrid") @db.VarChar(50)
  preferenceCurrency   String  @default("EUR") @db.VarChar(3)
  preferenceDateFormat String  @default("DD/MM/YYYY") @db.VarChar(20)
  notificationEmail    Boolean @default(true)
  notificationInvoice  Boolean @default(true)
  notificationPayment  Boolean @default(true)

  // Password reset
  resetPasswordToken   String?
  resetPasswordExpires DateTime?

  // Email verification
  emailVerificationToken String?
  emailVerified          Boolean   @default(false)
  emailVerifiedAt        DateTime?

  @@map("users")
}

// Customer Model
model Customer {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Basic information
  name      String  @db.VarChar(200)
  tradeName String? @db.VarChar(200)

  // Tax identification
  nif       String? @db.VarChar(9)
  cif       String? @db.VarChar(9)
  nie       String? @db.VarChar(9)
  vatNumber String? @db.VarChar(20)

  // Address
  address    String  @db.VarChar(255)
  city       String  @db.VarChar(100)
  state      String? @db.VarChar(100)
  postalCode String  @db.VarChar(10)
  country    String  @default("ES") @db.VarChar(2)

  // Contact information
  contactName   String? @db.VarChar(100)
  contactEmail  String? @db.VarChar(255)
  contactPhone  String? @db.VarChar(20)
  contactMobile String? @db.VarChar(20)

  // Business information
  businessType String?  @db.VarChar(50)
  website      String?  @db.VarChar(255)
  notes        String?  @db.VarChar(1000)
  tags         String[] @default([])

  // Status
  isActive Boolean @default(true)

  // External integration IDs
  externalWoocommerce String? @db.VarChar(50)
  externalShopify     String? @db.VarChar(50)
  externalPrestashop  String? @db.VarChar(50)

  // Company relation
  companyId String
  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)

  // Statistics
  statsTotalInvoices      Int       @default(0)
  statsTotalAmount        Decimal   @default(0) @db.Decimal(10, 2)
  statsLastInvoiceDate    DateTime?
  statsAveragePaymentDays Int       @default(0)

  // Relations
  invoices Invoice[]

  @@map("customers")
}

// Invoice Model
model Invoice {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Invoice identification
  invoiceNumber String @unique @db.VarChar(50)
  series        String @default("A") @db.VarChar(5)

  // Dates
  issueDate   DateTime @default(now())
  dueDate     DateTime
  serviceDate DateTime @default(now())

  // Relations
  customerId String
  customer   Customer @relation(fields: [customerId], references: [id], onDelete: Restrict)

  companyId String
  company   Company @relation(fields: [companyId], references: [id], onDelete: Cascade)

  // Invoice items
  items Json @default("[]")

  // Totals
  subtotal  Decimal @default(0) @db.Decimal(10, 2)
  taxAmount Decimal @default(0) @db.Decimal(10, 2)
  total     Decimal @default(0) @db.Decimal(10, 2)
  totalPaid Decimal @default(0) @db.Decimal(10, 2)
  totalDue  Decimal @default(0) @db.Decimal(10, 2)

  // Status
  status String @default("draft") @db.VarChar(20)

  // Payment information
  paymentStatus    String    @default("pending") @db.VarChar(20)
  paymentMethod    String?   @db.VarChar(50)
  paymentReference String?   @db.VarChar(100)
  paymentDate      DateTime?
  paymentNotes     String?   @db.VarChar(500)

  // Notes and references
  notes             String? @db.VarChar(1000)
  internalReference String? @db.VarChar(100)
  customerReference String? @db.VarChar(100)

  // External references
  externalWoocommerceOrderId String? @db.VarChar(50)
  externalShopifyOrderId     String? @db.VarChar(50)
  externalPrestashopOrderId  String? @db.VarChar(50)

  // Email tracking
  sentDate   DateTime?
  viewedDate DateTime?

  // PDF generation
  pdfGenerated Boolean @default(false)
  pdfPath      String? @db.VarChar(500)

  // Currency
  currency     String  @default("EUR") @db.VarChar(3)
  exchangeRate Decimal @default(1) @db.Decimal(10, 4)

  // Relations
  invoiceItems InvoiceItem[]

  @@map("invoices")
}

// Invoice Item Model (for better normalization)
model InvoiceItem {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Item details
  description String  @db.VarChar(500)
  quantity    Decimal @db.Decimal(10, 3)
  unitPrice   Decimal @db.Decimal(10, 2)
  taxRate     Decimal @db.Decimal(5, 2)
  taxAmount   Decimal @db.Decimal(10, 2)
  total       Decimal @db.Decimal(10, 2)

  // Product information
  productCode String? @db.VarChar(50)
  unit        String? @db.VarChar(20)

  // Invoice relation
  invoiceId String
  invoice   Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@map("invoice_items")
}
