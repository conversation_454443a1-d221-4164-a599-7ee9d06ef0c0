
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.11.1
 * Query Engine version: f40f79ec31188888a2e33acda0ecc8fd10a853a9
 */
Prisma.prismaVersion = {
  client: "6.11.1",
  engine: "f40f79ec31188888a2e33acda0ecc8fd10a853a9"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.CompanyScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  tradeName: 'tradeName',
  nif: 'nif',
  cif: 'cif',
  nie: 'nie',
  vatNumber: 'vatNumber',
  address: 'address',
  city: 'city',
  state: 'state',
  postalCode: 'postalCode',
  country: 'country',
  contactPhone: 'contactPhone',
  contactMobile: 'contactMobile',
  contactEmail: 'contactEmail',
  website: 'website',
  businessType: 'businessType',
  sector: 'sector',
  employees: 'employees',
  foundedYear: 'foundedYear',
  taxRates: 'taxRates',
  invoicePrefix: 'invoicePrefix',
  invoiceNumberLength: 'invoiceNumberLength',
  nextInvoiceNumber: 'nextInvoiceNumber',
  dueDays: 'dueDays',
  bankName: 'bankName',
  iban: 'iban',
  swift: 'swift',
  logo: 'logo',
  isActive: 'isActive',
  subscriptionPlan: 'subscriptionPlan',
  subscriptionStatus: 'subscriptionStatus',
  subscriptionExpiresAt: 'subscriptionExpiresAt',
  subscriptionMaxInvoicesPerMonth: 'subscriptionMaxInvoicesPerMonth'
};

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  email: 'email',
  password: 'password',
  firstName: 'firstName',
  lastName: 'lastName',
  role: 'role',
  isActive: 'isActive',
  lastLogin: 'lastLogin',
  apiKey: 'apiKey',
  companyId: 'companyId',
  preferenceLanguage: 'preferenceLanguage',
  preferenceTimezone: 'preferenceTimezone',
  preferenceCurrency: 'preferenceCurrency',
  preferenceDateFormat: 'preferenceDateFormat',
  notificationEmail: 'notificationEmail',
  notificationInvoice: 'notificationInvoice',
  notificationPayment: 'notificationPayment',
  resetPasswordToken: 'resetPasswordToken',
  resetPasswordExpires: 'resetPasswordExpires',
  emailVerificationToken: 'emailVerificationToken',
  emailVerified: 'emailVerified',
  emailVerifiedAt: 'emailVerifiedAt'
};

exports.Prisma.CustomerScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  name: 'name',
  tradeName: 'tradeName',
  nif: 'nif',
  cif: 'cif',
  nie: 'nie',
  vatNumber: 'vatNumber',
  address: 'address',
  city: 'city',
  state: 'state',
  postalCode: 'postalCode',
  country: 'country',
  contactName: 'contactName',
  contactEmail: 'contactEmail',
  contactPhone: 'contactPhone',
  contactMobile: 'contactMobile',
  businessType: 'businessType',
  website: 'website',
  notes: 'notes',
  tags: 'tags',
  isActive: 'isActive',
  externalWoocommerce: 'externalWoocommerce',
  externalShopify: 'externalShopify',
  externalPrestashop: 'externalPrestashop',
  companyId: 'companyId',
  statsTotalInvoices: 'statsTotalInvoices',
  statsTotalAmount: 'statsTotalAmount',
  statsLastInvoiceDate: 'statsLastInvoiceDate',
  statsAveragePaymentDays: 'statsAveragePaymentDays'
};

exports.Prisma.InvoiceScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  invoiceNumber: 'invoiceNumber',
  series: 'series',
  issueDate: 'issueDate',
  dueDate: 'dueDate',
  serviceDate: 'serviceDate',
  customerId: 'customerId',
  companyId: 'companyId',
  items: 'items',
  subtotal: 'subtotal',
  taxAmount: 'taxAmount',
  total: 'total',
  totalPaid: 'totalPaid',
  totalDue: 'totalDue',
  status: 'status',
  paymentStatus: 'paymentStatus',
  paymentMethod: 'paymentMethod',
  paymentReference: 'paymentReference',
  paymentDate: 'paymentDate',
  paymentNotes: 'paymentNotes',
  notes: 'notes',
  internalReference: 'internalReference',
  customerReference: 'customerReference',
  externalWoocommerceOrderId: 'externalWoocommerceOrderId',
  externalShopifyOrderId: 'externalShopifyOrderId',
  externalPrestashopOrderId: 'externalPrestashopOrderId',
  sentDate: 'sentDate',
  viewedDate: 'viewedDate',
  pdfGenerated: 'pdfGenerated',
  pdfPath: 'pdfPath',
  currency: 'currency',
  exchangeRate: 'exchangeRate'
};

exports.Prisma.InvoiceItemScalarFieldEnum = {
  id: 'id',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  description: 'description',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  taxRate: 'taxRate',
  taxAmount: 'taxAmount',
  total: 'total',
  productCode: 'productCode',
  unit: 'unit',
  invoiceId: 'invoiceId'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};


exports.Prisma.ModelName = {
  Company: 'Company',
  User: 'User',
  Customer: 'Customer',
  Invoice: 'Invoice',
  InvoiceItem: 'InvoiceItem'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
