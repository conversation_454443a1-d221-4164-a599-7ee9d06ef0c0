const puppeteer = require('puppeteer');
const handlebars = require('handlebars');
const fs = require('fs').promises;
const path = require('path');
const moment = require('moment-timezone');

// Configure moment for Spanish locale
moment.locale('es');

// Register Handlebars helpers
handlebars.registerHelper('formatCurrency', function(amount) {
  return new Intl.NumberFormat('es-ES', {
    style: 'currency',
    currency: 'EUR',
    minimumFractionDigits: 2
  }).format(amount || 0);
});

handlebars.registerHelper('formatDate', function(date) {
  return moment(date).tz('Europe/Madrid').format('DD/MM/YYYY');
});

handlebars.registerHelper('formatNumber', function(number, decimals = 2) {
  return new Intl.NumberFormat('es-ES', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(number || 0);
});

handlebars.registerHelper('eq', function(a, b) {
  return a === b;
});

handlebars.registerHelper('gt', function(a, b) {
  return a > b;
});

handlebars.registerHelper('add', function(a, b) {
  return (a || 0) + (b || 0);
});

handlebars.registerHelper('multiply', function(a, b) {
  return (a || 0) * (b || 0);
});

// Get invoice template
const getInvoiceTemplate = async () => {
  try {
    const templatePath = path.join(__dirname, '../../templates/invoice-spanish.html');
    const templateContent = await fs.readFile(templatePath, 'utf8');
    return handlebars.compile(templateContent);
  } catch (error) {
    console.error('Error loading invoice template:', error);
    // Fallback to inline template
    return handlebars.compile(getDefaultTemplate());
  }
};

// Default template if file doesn't exist
const getDefaultTemplate = () => {
  return `
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Factura {{invoiceNumber}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            background: white;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 20px;
        }
        
        .company-info {
            flex: 1;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #0066cc;
            margin-bottom: 10px;
        }
        
        .company-details {
            font-size: 11px;
            line-height: 1.3;
        }
        
        .invoice-title {
            text-align: right;
            flex: 1;
        }
        
        .invoice-title h1 {
            font-size: 28px;
            color: #0066cc;
            margin-bottom: 10px;
        }
        
        .invoice-number {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .invoice-dates {
            font-size: 11px;
        }
        
        .billing-section {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        
        .billing-info {
            flex: 1;
            margin-right: 20px;
        }
        
        .billing-info h3 {
            font-size: 14px;
            color: #0066cc;
            margin-bottom: 10px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .table th,
        .table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #0066cc;
            font-size: 11px;
        }
        
        .table td {
            font-size: 11px;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-center {
            text-align: center;
        }
        
        .totals-section {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
        }
        
        .totals-table {
            width: 300px;
        }
        
        .totals-table td {
            padding: 5px 10px;
            border: none;
        }
        
        .totals-table .total-row {
            font-weight: bold;
            font-size: 14px;
            background-color: #f8f9fa;
            border-top: 2px solid #0066cc;
        }
        
        .tax-breakdown {
            margin-top: 20px;
            font-size: 11px;
        }
        
        .tax-breakdown h4 {
            color: #0066cc;
            margin-bottom: 10px;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 10px;
            color: #666;
        }
        
        .payment-info {
            margin-top: 20px;
            background-color: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #0066cc;
        }
        
        .payment-info h4 {
            color: #0066cc;
            margin-bottom: 10px;
        }
        
        @media print {
            .container {
                padding: 0;
            }
            
            body {
                font-size: 11px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="company-info">
                <div class="company-name">{{company.name}}</div>
                <div class="company-details">
                    {{#if company.cif}}CIF: {{company.cif}}<br>{{/if}}
                    {{#if company.nif}}NIF: {{company.nif}}<br>{{/if}}
                    {{company.fullAddress}}<br>
                    {{#if company.contact.phone}}Tel: {{company.contact.phone}}<br>{{/if}}
                    {{#if company.contact.email}}Email: {{company.contact.email}}<br>{{/if}}
                    {{#if company.contact.website}}Web: {{company.contact.website}}{{/if}}
                </div>
            </div>
            <div class="invoice-title">
                <h1>FACTURA</h1>
                <div class="invoice-number">Nº {{invoiceNumber}}</div>
                <div class="invoice-dates">
                    <div>Fecha: {{formatDate issueDate}}</div>
                    <div>Vencimiento: {{formatDate dueDate}}</div>
                </div>
            </div>
        </div>

        <!-- Billing Information -->
        <div class="billing-section">
            <div class="billing-info">
                <h3>Facturar a:</h3>
                <div>
                    <strong>{{customer.displayName}}</strong><br>
                    {{#if customer.taxId}}{{customer.taxId}}<br>{{/if}}
                    {{customer.fullAddress}}<br>
                    {{#if customer.contact.email}}{{customer.contact.email}}<br>{{/if}}
                    {{#if customer.contact.phone}}{{customer.contact.phone}}{{/if}}
                </div>
            </div>
            {{#if externalReferences.orderNumber}}
            <div class="billing-info">
                <h3>Referencias:</h3>
                <div>
                    Nº Pedido: {{externalReferences.orderNumber}}
                </div>
            </div>
            {{/if}}
        </div>

        <!-- Invoice Lines -->
        <table class="table">
            <thead>
                <tr>
                    <th>Descripción</th>
                    <th class="text-center">Cant.</th>
                    <th class="text-right">Precio Unit.</th>
                    {{#if (gt totalDiscount 0)}}
                    <th class="text-right">Desc.</th>
                    {{/if}}
                    <th class="text-right">IVA</th>
                    <th class="text-right">Importe</th>
                </tr>
            </thead>
            <tbody>
                {{#each lines}}
                <tr>
                    <td>
                        {{description}}
                        {{#if productCode}}<br><small>Código: {{productCode}}</small>{{/if}}
                    </td>
                    <td class="text-center">{{formatNumber quantity}} {{unit}}</td>
                    <td class="text-right">{{formatCurrency unitPrice}}</td>
                    {{#if (gt ../totalDiscount 0)}}
                    <td class="text-right">{{formatNumber discount}}%</td>
                    {{/if}}
                    <td class="text-right">{{formatNumber taxRate}}%</td>
                    <td class="text-right">{{formatCurrency total}}</td>
                </tr>
                {{/each}}
            </tbody>
        </table>

        <!-- Totals -->
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td>Subtotal:</td>
                    <td class="text-right">{{formatCurrency subtotal}}</td>
                </tr>
                {{#if (gt totalDiscount 0)}}
                <tr>
                    <td>Descuento:</td>
                    <td class="text-right">-{{formatCurrency totalDiscount}}</td>
                </tr>
                {{/if}}
                <tr>
                    <td>Base Imponible:</td>
                    <td class="text-right">{{formatCurrency netAmount}}</td>
                </tr>
                <tr>
                    <td>IVA:</td>
                    <td class="text-right">{{formatCurrency totalTax}}</td>
                </tr>
                {{#if (gt compliance.irpfAmount 0)}}
                <tr>
                    <td>IRPF ({{formatNumber compliance.irpfRate}}%):</td>
                    <td class="text-right">-{{formatCurrency compliance.irpfAmount}}</td>
                </tr>
                {{/if}}
                <tr class="total-row">
                    <td><strong>TOTAL:</strong></td>
                    <td class="text-right"><strong>{{formatCurrency total}}</strong></td>
                </tr>
            </table>
        </div>

        <!-- Tax Breakdown -->
        {{#if taxBreakdown}}
        <div class="tax-breakdown">
            <h4>Desglose de IVA:</h4>
            <table class="table">
                <thead>
                    <tr>
                        <th>Tipo</th>
                        <th class="text-right">Base Imponible</th>
                        <th class="text-right">Tipo IVA</th>
                        <th class="text-right">Cuota IVA</th>
                    </tr>
                </thead>
                <tbody>
                    {{#each taxBreakdown}}
                    <tr>
                        <td>{{taxName}}</td>
                        <td class="text-right">{{formatCurrency taxableAmount}}</td>
                        <td class="text-right">{{formatNumber taxRate}}%</td>
                        <td class="text-right">{{formatCurrency taxAmount}}</td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
        </div>
        {{/if}}

        <!-- Payment Information -->
        {{#if company.bankInfo.iban}}
        <div class="payment-info">
            <h4>Información de Pago</h4>
            <div>
                <strong>IBAN:</strong> {{company.bankInfo.iban}}<br>
                {{#if company.bankInfo.bankName}}<strong>Banco:</strong> {{company.bankInfo.bankName}}<br>{{/if}}
                {{#if company.bankInfo.swift}}<strong>SWIFT:</strong> {{company.bankInfo.swift}}<br>{{/if}}
                <strong>Vencimiento:</strong> {{formatDate dueDate}}
            </div>
        </div>
        {{/if}}

        <!-- Notes -->
        {{#if notes}}
        <div class="payment-info">
            <h4>Observaciones</h4>
            <div>{{notes}}</div>
        </div>
        {{/if}}

        <!-- Footer -->
        <div class="footer">
            <div>
                Esta factura ha sido generada electrónicamente por VeriAPI.
                {{#if compliance.reverseCharge}}
                <br><strong>Inversión del sujeto pasivo - Reverse Charge</strong>
                {{/if}}
            </div>
        </div>
    </div>
</body>
</html>
  `;
};

// Generate PDF from invoice data
const generateInvoicePDF = async (invoice) => {
  let browser;
  
  try {
    // Get template
    const template = await getInvoiceTemplate();
    
    // Prepare data for template
    const templateData = {
      ...invoice.toObject(),
      company: invoice.company,
      customer: invoice.customer
    };
    
    // Generate HTML
    const html = template(templateData);
    
    // Launch Puppeteer
    browser = await puppeteer.launch({
      headless: 'new',
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // Set content
    await page.setContent(html, {
      waitUntil: 'networkidle0'
    });
    
    // Generate PDF
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '20mm',
        right: '15mm',
        bottom: '20mm',
        left: '15mm'
      }
    });
    
    return pdfBuffer;
    
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  } finally {
    if (browser) {
      await browser.close();
    }
  }
};

module.exports = generateInvoicePDF;
