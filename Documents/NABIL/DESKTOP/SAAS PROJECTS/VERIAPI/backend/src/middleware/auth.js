const jwt = require('jsonwebtoken');
const { User } = require('../models/prisma');

// JWT Authentication middleware
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        error: 'Token de acceso requerido',
        message: 'Debes proporcionar un token de autenticación válido'
      });
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Get user from database
    const user = await User.findById(decoded.userId);

    if (!user) {
      return res.status(401).json({
        error: 'Usuario no encontrado',
        message: 'El token no corresponde a un usuario válido'
      });
    }

    if (!user.isActive) {
      return res.status(401).json({
        error: 'Usuario inactivo',
        message: 'Tu cuenta ha sido desactivada'
      });
    }

    if (!user.company.isActive) {
      return res.status(401).json({
        error: 'Empresa inactiva',
        message: 'La empresa asociada ha sido desactivada'
      });
    }

    // Add user to request object
    req.user = user;
    req.company = user.company;
    next();

  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: 'Token inválido',
        message: 'El token de autenticación no es válido'
      });
    }

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'Token expirado',
        message: 'El token de autenticación ha expirado'
      });
    }

    console.error('Error en autenticación:', error);
    return res.status(500).json({
      error: 'Error interno del servidor',
      message: 'Error al verificar la autenticación'
    });
  }
};

// API Key Authentication middleware
const authenticateApiKey = async (req, res, next) => {
  try {
    const apiKey = req.headers['x-api-key'];

    if (!apiKey) {
      return res.status(401).json({
        error: 'API Key requerida',
        message: 'Debes proporcionar una API Key válida en el header X-API-Key'
      });
    }

    // Find user by API key
    const user = await User.findByApiKey(apiKey);

    if (!user || !user.isActive) {
      return res.status(401).json({
        error: 'API Key inválida',
        message: 'La API Key proporcionada no es válida o ha sido desactivada'
      });
    }

    if (!user.company.isActive) {
      return res.status(401).json({
        error: 'Empresa inactiva',
        message: 'La empresa asociada a esta API Key ha sido desactivada'
      });
    }

    // Add user to request object
    req.user = user;
    req.company = user.company;
    next();

  } catch (error) {
    console.error('Error en autenticación API Key:', error);
    return res.status(500).json({
      error: 'Error interno del servidor',
      message: 'Error al verificar la API Key'
    });
  }
};

// Combined authentication middleware (JWT or API Key)
const authenticate = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const apiKey = req.headers['x-api-key'];

  if (authHeader && authHeader.startsWith('Bearer ')) {
    // Use JWT authentication
    return authenticateToken(req, res, next);
  } else if (apiKey) {
    // Use API Key authentication
    return authenticateApiKey(req, res, next);
  } else {
    return res.status(401).json({
      error: 'Autenticación requerida',
      message: 'Debes proporcionar un token Bearer o una API Key'
    });
  }
};

// Role-based authorization middleware
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'No autenticado',
        message: 'Debes estar autenticado para acceder a este recurso'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        error: 'Acceso denegado',
        message: `Necesitas uno de estos roles: ${roles.join(', ')}`
      });
    }

    next();
  };
};

// Company ownership middleware
const requireCompanyOwnership = (req, res, next) => {
  if (!req.user || !req.company) {
    return res.status(401).json({
      error: 'No autenticado',
      message: 'Debes estar autenticado para acceder a este recurso'
    });
  }

  // Admin users can access any company
  if (req.user.role === 'admin') {
    return next();
  }

  // Check if the resource belongs to the user's company
  const resourceCompanyId = req.params.companyId || req.body.company || req.query.company;
  
  if (resourceCompanyId && resourceCompanyId !== req.company._id.toString()) {
    return res.status(403).json({
      error: 'Acceso denegado',
      message: 'No tienes permisos para acceder a los recursos de esta empresa'
    });
  }

  next();
};

// Rate limiting by user
const userRateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const requests = new Map();

  return (req, res, next) => {
    if (!req.user) {
      return next();
    }

    const userId = req.user._id.toString();
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean old requests
    if (requests.has(userId)) {
      const userRequests = requests.get(userId);
      const validRequests = userRequests.filter(time => time > windowStart);
      requests.set(userId, validRequests);
    }

    // Get current requests for user
    const userRequests = requests.get(userId) || [];

    if (userRequests.length >= maxRequests) {
      return res.status(429).json({
        error: 'Demasiadas solicitudes',
        message: `Has excedido el límite de ${maxRequests} solicitudes por ${windowMs / 60000} minutos`,
        retryAfter: Math.ceil((userRequests[0] + windowMs - now) / 1000)
      });
    }

    // Add current request
    userRequests.push(now);
    requests.set(userId, userRequests);

    next();
  };
};

module.exports = {
  authenticate,
  authenticateToken,
  authenticateApiKey,
  authorize,
  requireCompanyOwnership,
  userRateLimit
};
