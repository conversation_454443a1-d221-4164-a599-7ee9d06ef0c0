const { PrismaClient } = require('../generated/prisma');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  try {
    // Create Company 1
    const company1 = await prisma.company.create({
      data: {
        name: 'Soloun Technologies S.L.',
        tradeName: 'Soloun Tech',
        cif: 'B12345678',
        address: 'Calle Tecnología 123',
        city: 'Madrid',
        postalCode: '28001',
        country: 'ES',
        contactPhone: '+34 91 123 45 67',
        contactEmail: '<EMAIL>',
        website: 'https://www.soloun.link',
        businessType: 'sociedad_limitada',
        sector: 'Tecnología',
        employees: 25,
        foundedYear: 2020,
        iban: '************************',
        bankName: 'Banco Santander',
        invoicePrefix: 'SOL',
        nextInvoiceNumber: 1,
      },
    });

    // Create Company 2
    const company2 = await prisma.company.create({
      data: {
        name: 'Innovación Digital S.L.',
        tradeName: 'InnovaDigital',
        cif: 'B87654321',
        address: 'Avenida Innovación 456',
        city: 'Barcelona',
        postalCode: '08001',
        country: 'ES',
        contactPhone: '+34 93 987 65 43',
        contactEmail: '<EMAIL>',
        website: 'https://www.innovadigital.es',
        businessType: 'sociedad_limitada',
        sector: 'Marketing Digital',
        employees: 15,
        foundedYear: 2019,
        iban: '************************',
        bankName: 'BBVA',
        invoicePrefix: 'INN',
        nextInvoiceNumber: 1,
      },
    });

    console.log('✅ Companies created');

    // Create Users
    const hashedPassword = await bcrypt.hash('password123', 12);

    const user1 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Carlos',
        lastName: 'Rodríguez',
        role: 'admin',
        companyId: company1.id,
        emailVerified: true,
        emailVerifiedAt: new Date(),
      },
    });

    const user2 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'María',
        lastName: 'González',
        role: 'admin',
        companyId: company2.id,
        emailVerified: true,
        emailVerifiedAt: new Date(),
      },
    });

    const user3 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: 'Juan',
        lastName: 'Martínez',
        role: 'user',
        companyId: company1.id,
        emailVerified: true,
        emailVerifiedAt: new Date(),
      },
    });

    console.log('✅ Users created');

    // Create Customers for Company 1
    const customer1 = await prisma.customer.create({
      data: {
        name: 'Empresa Cliente S.L.',
        tradeName: 'Cliente Uno',
        cif: 'B11111111',
        address: 'Calle Cliente 789',
        city: 'Madrid',
        postalCode: '28002',
        country: 'ES',
        contactName: 'Ana López',
        contactEmail: '<EMAIL>',
        contactPhone: '+34 91 111 11 11',
        businessType: 'sociedad_limitada',
        companyId: company1.id,
        tags: ['VIP', 'Tecnología'],
      },
    });

    const customer2 = await prisma.customer.create({
      data: {
        name: 'Comercial Dos S.L.',
        tradeName: 'Comercial Dos',
        cif: 'B22222222',
        address: 'Avenida Comercio 321',
        city: 'Valencia',
        postalCode: '46001',
        country: 'ES',
        contactName: 'Pedro Sánchez',
        contactEmail: '<EMAIL>',
        contactPhone: '+34 96 222 22 22',
        businessType: 'sociedad_limitada',
        companyId: company1.id,
        tags: ['Comercio'],
      },
    });

    // Create Customer for Company 2
    const customer3 = await prisma.customer.create({
      data: {
        name: 'Servicios Tres S.L.',
        tradeName: 'Servicios Tres',
        cif: 'B33333333',
        address: 'Plaza Servicios 654',
        city: 'Barcelona',
        postalCode: '08002',
        country: 'ES',
        contactName: 'Laura Fernández',
        contactEmail: '<EMAIL>',
        contactPhone: '+34 93 333 33 33',
        businessType: 'sociedad_limitada',
        companyId: company2.id,
        tags: ['Servicios', 'Premium'],
      },
    });

    console.log('✅ Customers created');

    // Create Invoices
    const invoiceItems1 = [
      {
        description: 'Desarrollo de aplicación web',
        quantity: 1,
        unitPrice: 2500.00,
        taxRate: 21,
        taxAmount: 525.00,
        total: 3025.00,
        productCode: 'DEV-WEB-001',
        unit: 'proyecto'
      },
      {
        description: 'Mantenimiento mensual',
        quantity: 3,
        unitPrice: 150.00,
        taxRate: 21,
        taxAmount: 94.50,
        total: 544.50,
        productCode: 'MANT-001',
        unit: 'mes'
      }
    ];

    const invoice1 = await prisma.invoice.create({
      data: {
        invoiceNumber: 'SOL000001',
        series: 'SOL',
        issueDate: new Date('2024-06-01'),
        dueDate: new Date('2024-07-01'),
        serviceDate: new Date('2024-06-01'),
        customerId: customer1.id,
        companyId: company1.id,
        items: invoiceItems1,
        subtotal: 2950.00,
        taxAmount: 619.50,
        total: 3569.50,
        totalDue: 3569.50,
        status: 'sent',
        paymentStatus: 'pending',
        currency: 'EUR',
        notes: 'Factura por desarrollo de aplicación web personalizada',
        sentDate: new Date('2024-06-01'),
      },
    });

    // Create more invoices
    const invoiceItems2 = [
      {
        description: 'Consultoría SEO',
        quantity: 1,
        unitPrice: 800.00,
        taxRate: 21,
        taxAmount: 168.00,
        total: 968.00,
        productCode: 'SEO-001',
        unit: 'proyecto'
      }
    ];

    const invoice2 = await prisma.invoice.create({
      data: {
        invoiceNumber: 'SOL000002',
        series: 'SOL',
        issueDate: new Date('2024-06-15'),
        dueDate: new Date('2024-07-15'),
        serviceDate: new Date('2024-06-15'),
        customerId: customer2.id,
        companyId: company1.id,
        items: invoiceItems2,
        subtotal: 800.00,
        taxAmount: 168.00,
        total: 968.00,
        totalDue: 968.00,
        status: 'sent',
        paymentStatus: 'pending',
        currency: 'EUR',
        notes: 'Consultoría de optimización SEO',
        sentDate: new Date('2024-06-15'),
      },
    });

    const invoiceItems3 = [
      {
        description: 'Diseño de identidad corporativa',
        quantity: 1,
        unitPrice: 1200.00,
        taxRate: 21,
        taxAmount: 252.00,
        total: 1452.00,
        productCode: 'DIS-001',
        unit: 'proyecto'
      }
    ];

    const invoice3 = await prisma.invoice.create({
      data: {
        invoiceNumber: 'INN000001',
        series: 'INN',
        issueDate: new Date('2024-06-20'),
        dueDate: new Date('2024-07-20'),
        serviceDate: new Date('2024-06-20'),
        customerId: customer3.id,
        companyId: company2.id,
        items: invoiceItems3,
        subtotal: 1200.00,
        taxAmount: 252.00,
        total: 1452.00,
        totalDue: 1452.00,
        status: 'sent',
        paymentStatus: 'pending',
        currency: 'EUR',
        notes: 'Diseño completo de identidad corporativa',
        sentDate: new Date('2024-06-20'),
      },
    });

    // Create paid invoice
    const invoiceItems4 = [
      {
        description: 'Mantenimiento web mensual',
        quantity: 3,
        unitPrice: 120.00,
        taxRate: 21,
        taxAmount: 75.60,
        total: 435.60,
        productCode: 'MANT-WEB-001',
        unit: 'mes'
      }
    ];

    const invoice4 = await prisma.invoice.create({
      data: {
        invoiceNumber: 'SOL000003',
        series: 'SOL',
        issueDate: new Date('2024-05-01'),
        dueDate: new Date('2024-06-01'),
        serviceDate: new Date('2024-05-01'),
        customerId: customer1.id,
        companyId: company1.id,
        items: invoiceItems4,
        subtotal: 360.00,
        taxAmount: 75.60,
        total: 435.60,
        totalDue: 0.00,
        totalPaid: 435.60,
        status: 'sent',
        paymentStatus: 'paid',
        paymentMethod: 'bank_transfer',
        paymentDate: new Date('2024-05-25'),
        currency: 'EUR',
        notes: 'Mantenimiento web trimestral',
        sentDate: new Date('2024-05-01'),
      },
    });

    // Create overdue invoice
    const invoiceItems5 = [
      {
        description: 'Auditoría de seguridad',
        quantity: 1,
        unitPrice: 600.00,
        taxRate: 21,
        taxAmount: 126.00,
        total: 726.00,
        productCode: 'AUD-SEC-001',
        unit: 'proyecto'
      }
    ];

    const invoice5 = await prisma.invoice.create({
      data: {
        invoiceNumber: 'SOL000004',
        series: 'SOL',
        issueDate: new Date('2024-04-01'),
        dueDate: new Date('2024-05-01'),
        serviceDate: new Date('2024-04-01'),
        customerId: customer2.id,
        companyId: company1.id,
        items: invoiceItems5,
        subtotal: 600.00,
        taxAmount: 126.00,
        total: 726.00,
        totalDue: 726.00,
        status: 'sent',
        paymentStatus: 'pending',
        currency: 'EUR',
        notes: 'Auditoría completa de seguridad web',
        sentDate: new Date('2024-04-01'),
      },
    });

    console.log('✅ All invoices created');

    // Update customer statistics
    await prisma.customer.update({
      where: { id: customer1.id },
      data: {
        statsTotalInvoices: 2,
        statsTotalAmount: 4005.10,
        statsLastInvoiceDate: new Date('2024-06-01'),
      },
    });

    await prisma.customer.update({
      where: { id: customer2.id },
      data: {
        statsTotalInvoices: 2,
        statsTotalAmount: 1694.00,
        statsLastInvoiceDate: new Date('2024-06-15'),
      },
    });

    await prisma.customer.update({
      where: { id: customer3.id },
      data: {
        statsTotalInvoices: 1,
        statsTotalAmount: 1452.00,
        statsLastInvoiceDate: new Date('2024-06-20'),
      },
    });

    console.log('✅ Customer statistics updated');
    console.log('🎉 Database seeding completed successfully!');

    console.log('\n📊 Summary:');
    console.log(`- Companies: 2`);
    console.log(`- Users: 3`);
    console.log(`- Customers: 3`);
    console.log(`- Invoices: 5`);

    console.log('\n🔑 Login credentials:');
    console.log('Company 1 (Soloun): <EMAIL> / password123');
    console.log('Company 2 (InnovaDigital): <EMAIL> / password123');
    console.log('User (Soloun): <EMAIL> / password123');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
