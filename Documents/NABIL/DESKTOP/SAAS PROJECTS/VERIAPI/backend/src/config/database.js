const { PrismaClient } = require('../generated/prisma');

// Create a single instance of Prisma Client
const prisma = new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
});

const connectDB = async () => {
  try {
    // Test the connection
    await prisma.$connect();
    console.log('📊 PostgreSQL conectado exitosamente');

    // Graceful shutdown
    process.on('SIGINT', async () => {
      await prisma.$disconnect();
      console.log('📊 Conexión PostgreSQL cerrada por terminación de aplicación');
      process.exit(0);
    });

    process.on('SIGTERM', async () => {
      await prisma.$disconnect();
      console.log('📊 Conexión PostgreSQL cerrada por SIGTERM');
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ Error conectando a PostgreSQL:', error.message);
    process.exit(1);
  }
};

// Export both the connection function and the prisma instance
module.exports = { connectDB, prisma };
