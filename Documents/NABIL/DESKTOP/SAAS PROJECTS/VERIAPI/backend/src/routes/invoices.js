const express = require('express');
const { body, query, validationResult } = require('express-validator');
const Invoice = require('../models/Invoice');
const Customer = require('../models/Customer');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const { authorize, requireCompanyOwnership } = require('../middleware/auth');

const router = express.Router();

// Validation rules
const createInvoiceValidation = [
  body('customer')
    .isMongoId()
    .withMessage('ID de cliente inválido'),
  body('lines')
    .isArray({ min: 1 })
    .withMessage('La factura debe tener al menos una línea'),
  body('lines.*.description')
    .trim()
    .isLength({ min: 1, max: 500 })
    .withMessage('La descripción es obligatoria y no puede exceder 500 caracteres'),
  body('lines.*.quantity')
    .isFloat({ min: 0.01 })
    .withMessage('La cantidad debe ser mayor a 0'),
  body('lines.*.unitPrice')
    .isFloat({ min: 0 })
    .withMessage('El precio unitario no puede ser negativo'),
  body('lines.*.taxRate')
    .isFloat({ min: 0, max: 100 })
    .withMessage('El tipo de IVA debe estar entre 0 y 100'),
  body('dueDate')
    .optional()
    .isISO8601()
    .withMessage('Fecha de vencimiento inválida'),
  body('serviceDate')
    .optional()
    .isISO8601()
    .withMessage('Fecha de servicio inválida')
];

const updateInvoiceValidation = [
  body('lines')
    .optional()
    .isArray({ min: 1 })
    .withMessage('La factura debe tener al menos una línea'),
  body('lines.*.description')
    .optional()
    .trim()
    .isLength({ min: 1, max: 500 })
    .withMessage('La descripción no puede exceder 500 caracteres'),
  body('lines.*.quantity')
    .optional()
    .isFloat({ min: 0.01 })
    .withMessage('La cantidad debe ser mayor a 0'),
  body('lines.*.unitPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('El precio unitario no puede ser negativo'),
  body('lines.*.taxRate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('El tipo de IVA debe estar entre 0 y 100')
];

// @route   GET /api/invoices
// @desc    Get all invoices for company
// @access  Private
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('Página debe ser un número mayor a 0'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Límite debe estar entre 1 y 100'),
  query('status').optional().isIn(['draft', 'sent', 'viewed', 'paid', 'overdue', 'cancelled']),
  query('customer').optional().isMongoId().withMessage('ID de cliente inválido')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Parámetros de consulta inválidos',
      details: errors.array()
    });
  }

  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;
  const skip = (page - 1) * limit;

  // Build filter
  const filter = { company: req.company._id };
  
  if (req.query.status) {
    filter.status = req.query.status;
  }
  
  if (req.query.customer) {
    filter.customer = req.query.customer;
  }

  if (req.query.search) {
    filter.$or = [
      { invoiceNumber: { $regex: req.query.search, $options: 'i' } },
      { 'externalReferences.orderNumber': { $regex: req.query.search, $options: 'i' } }
    ];
  }

  // Date range filter
  if (req.query.startDate || req.query.endDate) {
    filter.issueDate = {};
    if (req.query.startDate) {
      filter.issueDate.$gte = new Date(req.query.startDate);
    }
    if (req.query.endDate) {
      filter.issueDate.$lte = new Date(req.query.endDate);
    }
  }

  // Execute query
  const [invoices, total] = await Promise.all([
    Invoice.find(filter)
      .populate('customer', 'name tradeName taxId contact.email')
      .sort({ issueDate: -1 })
      .skip(skip)
      .limit(limit),
    Invoice.countDocuments(filter)
  ]);

  res.json({
    invoices,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit),
      hasNext: page < Math.ceil(total / limit),
      hasPrev: page > 1
    }
  });
}));

// @route   GET /api/invoices/:id
// @desc    Get single invoice
// @access  Private
router.get('/:id', catchAsync(async (req, res) => {
  const invoice = await Invoice.findOne({
    _id: req.params.id,
    company: req.company._id
  })
    .populate('customer')
    .populate('company');

  if (!invoice) {
    throw new AppError('Factura no encontrada', 404, 'INVOICE_NOT_FOUND');
  }

  res.json({ invoice });
}));

// @route   POST /api/invoices
// @desc    Create new invoice
// @access  Private
router.post('/', createInvoiceValidation, catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Datos de factura inválidos',
      details: errors.array()
    });
  }

  // Verify customer belongs to company
  const customer = await Customer.findOne({
    _id: req.body.customer,
    company: req.company._id
  });

  if (!customer) {
    throw new AppError('Cliente no encontrado', 404, 'CUSTOMER_NOT_FOUND');
  }

  // Generate invoice number
  const invoiceNumber = await Invoice.generateInvoiceNumber(req.company._id);

  // Calculate due date if not provided
  let dueDate = req.body.dueDate;
  if (!dueDate) {
    dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + (customer.paymentInfo.paymentTerms || req.company.invoiceSettings.dueDays || 30));
  }

  // Create invoice
  const invoice = new Invoice({
    invoiceNumber,
    customer: customer._id,
    company: req.company._id,
    lines: req.body.lines,
    dueDate,
    serviceDate: req.body.serviceDate || new Date(),
    notes: req.body.notes,
    externalReferences: req.body.externalReferences,
    compliance: {
      irpfRate: req.body.irpfRate || 0,
      reverseCharge: req.body.reverseCharge || false,
      specialRegime: req.body.specialRegime || 'general'
    }
  });

  await invoice.save();

  // Update customer statistics
  await customer.updateStats();

  // Populate for response
  await invoice.populate('customer', 'name tradeName taxId contact.email');

  res.status(201).json({
    message: 'Factura creada exitosamente',
    invoice
  });
}));

// @route   PUT /api/invoices/:id
// @desc    Update invoice
// @access  Private
router.put('/:id', updateInvoiceValidation, catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Datos de actualización inválidos',
      details: errors.array()
    });
  }

  const invoice = await Invoice.findOne({
    _id: req.params.id,
    company: req.company._id
  });

  if (!invoice) {
    throw new AppError('Factura no encontrada', 404, 'INVOICE_NOT_FOUND');
  }

  // Check if invoice can be modified
  if (invoice.status === 'paid') {
    throw new AppError('No se puede modificar una factura pagada', 400, 'INVOICE_PAID');
  }

  // Update allowed fields
  const allowedUpdates = ['lines', 'dueDate', 'serviceDate', 'notes', 'compliance'];
  allowedUpdates.forEach(field => {
    if (req.body[field] !== undefined) {
      if (field === 'compliance') {
        invoice.compliance = { ...invoice.compliance, ...req.body[field] };
      } else {
        invoice[field] = req.body[field];
      }
    }
  });

  await invoice.save();

  // Update customer statistics
  const customer = await Customer.findById(invoice.customer);
  if (customer) {
    await customer.updateStats();
  }

  await invoice.populate('customer', 'name tradeName taxId contact.email');

  res.json({
    message: 'Factura actualizada exitosamente',
    invoice
  });
}));

// @route   DELETE /api/invoices/:id
// @desc    Delete invoice (only drafts)
// @access  Private
router.delete('/:id', authorize('admin'), catchAsync(async (req, res) => {
  const invoice = await Invoice.findOne({
    _id: req.params.id,
    company: req.company._id
  });

  if (!invoice) {
    throw new AppError('Factura no encontrada', 404, 'INVOICE_NOT_FOUND');
  }

  if (invoice.status !== 'draft') {
    throw new AppError('Solo se pueden eliminar facturas en borrador', 400, 'CANNOT_DELETE_INVOICE');
  }

  await Invoice.findByIdAndDelete(req.params.id);

  // Update customer statistics
  const customer = await Customer.findById(invoice.customer);
  if (customer) {
    await customer.updateStats();
  }

  res.json({
    message: 'Factura eliminada exitosamente'
  });
}));

// @route   PATCH /api/invoices/:id/status
// @desc    Update invoice status
// @access  Private
router.patch('/:id/status', [
  body('status')
    .isIn(['draft', 'sent', 'viewed', 'paid', 'overdue', 'cancelled'])
    .withMessage('Estado de factura inválido')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Estado inválido',
      details: errors.array()
    });
  }

  const invoice = await Invoice.findOne({
    _id: req.params.id,
    company: req.company._id
  });

  if (!invoice) {
    throw new AppError('Factura no encontrada', 404, 'INVOICE_NOT_FOUND');
  }

  const oldStatus = invoice.status;
  invoice.status = req.body.status;

  // Update timestamps based on status
  if (req.body.status === 'sent' && oldStatus === 'draft') {
    invoice.sentDate = new Date();
  } else if (req.body.status === 'viewed' && !invoice.viewedDate) {
    invoice.viewedDate = new Date();
  }

  await invoice.save();

  res.json({
    message: 'Estado de factura actualizado exitosamente',
    invoice: {
      id: invoice._id,
      invoiceNumber: invoice.invoiceNumber,
      status: invoice.status,
      sentDate: invoice.sentDate,
      viewedDate: invoice.viewedDate
    }
  });
}));

// @route   POST /api/invoices/:id/payment
// @desc    Record payment for invoice
// @access  Private
router.post('/:id/payment', [
  body('amount')
    .isFloat({ min: 0.01 })
    .withMessage('El importe debe ser mayor a 0'),
  body('paymentDate')
    .optional()
    .isISO8601()
    .withMessage('Fecha de pago inválida'),
  body('method')
    .optional()
    .isIn(['transfer', 'card', 'cash', 'check', 'other'])
    .withMessage('Método de pago inválido'),
  body('reference')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('La referencia no puede exceder 100 caracteres')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Datos de pago inválidos',
      details: errors.array()
    });
  }

  const invoice = await Invoice.findOne({
    _id: req.params.id,
    company: req.company._id
  });

  if (!invoice) {
    throw new AppError('Factura no encontrada', 404, 'INVOICE_NOT_FOUND');
  }

  const { amount, paymentDate, method, reference } = req.body;

  // Validate payment amount
  const remainingAmount = invoice.total - invoice.paymentInfo.paidAmount;
  if (amount > remainingAmount) {
    throw new AppError(
      `El importe excede la cantidad pendiente (${remainingAmount.toFixed(2)} €)`,
      400,
      'PAYMENT_EXCEEDS_REMAINING'
    );
  }

  // Update payment info
  invoice.paymentInfo.paidAmount += amount;
  invoice.paymentInfo.paidDate = paymentDate || new Date();
  if (method) invoice.paymentInfo.method = method;
  if (reference) invoice.paymentInfo.reference = reference;

  // Update payment status
  if (invoice.paymentInfo.paidAmount >= invoice.total) {
    invoice.paymentInfo.status = 'paid';
    invoice.status = 'paid';
  } else {
    invoice.paymentInfo.status = 'partial';
  }

  await invoice.save();

  // Update customer statistics
  const customer = await Customer.findById(invoice.customer);
  if (customer) {
    await customer.updateStats();
  }

  res.json({
    message: 'Pago registrado exitosamente',
    invoice: {
      id: invoice._id,
      invoiceNumber: invoice.invoiceNumber,
      total: invoice.total,
      paidAmount: invoice.paymentInfo.paidAmount,
      remainingAmount: invoice.total - invoice.paymentInfo.paidAmount,
      paymentStatus: invoice.paymentInfo.status,
      status: invoice.status
    }
  });
}));

// @route   GET /api/invoices/:id/pdf
// @desc    Generate and download invoice PDF
// @access  Private
router.get('/:id/pdf', catchAsync(async (req, res) => {
  const invoice = await Invoice.findOne({
    _id: req.params.id,
    company: req.company._id
  })
    .populate('customer')
    .populate('company');

  if (!invoice) {
    throw new AppError('Factura no encontrada', 404, 'INVOICE_NOT_FOUND');
  }

  // Import PDF generator (we'll create this next)
  const generateInvoicePDF = require('../utils/pdfGenerator');

  try {
    const pdfBuffer = await generateInvoicePDF(invoice);

    // Set response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="factura-${invoice.invoiceNumber}.pdf"`);
    res.setHeader('Content-Length', pdfBuffer.length);

    // Send PDF
    res.send(pdfBuffer);

    // Update invoice status if it was a draft
    if (invoice.status === 'draft') {
      invoice.status = 'sent';
      invoice.sentDate = new Date();
      await invoice.save();
    }

  } catch (error) {
    console.error('Error generando PDF:', error);
    throw new AppError('Error al generar el PDF de la factura', 500, 'PDF_GENERATION_ERROR');
  }
}));

// @route   GET /api/invoices/stats/summary
// @desc    Get invoice statistics summary
// @access  Private
router.get('/stats/summary', catchAsync(async (req, res) => {
  const companyId = req.company._id;

  const stats = await Invoice.aggregate([
    { $match: { company: companyId } },
    {
      $group: {
        _id: null,
        totalInvoices: { $sum: 1 },
        totalAmount: { $sum: '$total' },
        paidAmount: { $sum: '$paymentInfo.paidAmount' },
        pendingAmount: {
          $sum: {
            $subtract: ['$total', '$paymentInfo.paidAmount']
          }
        },
        draftCount: {
          $sum: { $cond: [{ $eq: ['$status', 'draft'] }, 1, 0] }
        },
        sentCount: {
          $sum: { $cond: [{ $eq: ['$status', 'sent'] }, 1, 0] }
        },
        paidCount: {
          $sum: { $cond: [{ $eq: ['$status', 'paid'] }, 1, 0] }
        },
        overdueCount: {
          $sum: { $cond: [{ $eq: ['$status', 'overdue'] }, 1, 0] }
        }
      }
    }
  ]);

  const summary = stats[0] || {
    totalInvoices: 0,
    totalAmount: 0,
    paidAmount: 0,
    pendingAmount: 0,
    draftCount: 0,
    sentCount: 0,
    paidCount: 0,
    overdueCount: 0
  };

  res.json({ summary });
}));

module.exports = router;
