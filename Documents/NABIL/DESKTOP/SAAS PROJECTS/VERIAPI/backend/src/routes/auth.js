const express = require('express');
const jwt = require('jsonwebtoken');
const { body, validationResult } = require('express-validator');
const { User, Company } = require('../models/prisma');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const { authenticate } = require('../middleware/auth');

const router = express.Router();

// Generate JWT token
const generateToken = (userId) => {
  return jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );
};

// Validation rules
const registerValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Debe ser un email válido'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('La contraseña debe tener al menos 6 caracteres'),
  body('firstName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('El nombre debe tener entre 2 y 50 caracteres'),
  body('lastName')
    .trim()
    .isLength({ min: 2, max: 50 })
    .withMessage('El apellido debe tener entre 2 y 50 caracteres'),
  body('companyName')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('El nombre de la empresa debe tener entre 2 y 200 caracteres'),
  body('companyEmail')
    .isEmail()
    .normalizeEmail()
    .withMessage('Debe ser un email válido para la empresa'),
  body('companyAddress')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('La dirección debe tener entre 5 y 200 caracteres'),
  body('companyCity')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('La ciudad debe tener entre 2 y 100 caracteres'),
  body('companyPostalCode')
    .matches(/^[0-9]{5}$/)
    .withMessage('El código postal debe tener 5 dígitos')
];

const loginValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Debe ser un email válido'),
  body('password')
    .notEmpty()
    .withMessage('La contraseña es obligatoria')
];

// @route   POST /api/auth/register
// @desc    Register new user and company
// @access  Public
router.post('/register', registerValidation, catchAsync(async (req, res) => {
  // Check validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Datos de registro inválidos',
      details: errors.array()
    });
  }

  const {
    email,
    password,
    firstName,
    lastName,
    companyName,
    companyEmail,
    companyAddress,
    companyCity,
    companyPostalCode,
    companyPhone,
    companyCif,
    companyNif
  } = req.body;

  // Check if user already exists
  const existingUser = await User.findByEmail(email);
  if (existingUser) {
    throw new AppError('Ya existe un usuario con este email', 400, 'USER_EXISTS');
  }

  // Create company first
  const company = await Company.create({
    name: companyName,
    address: companyAddress,
    city: companyCity,
    postalCode: companyPostalCode,
    country: 'ES',
    contactEmail: companyEmail,
    contactPhone: companyPhone,
    cif: companyCif,
    nif: companyNif
  });

  // Create user
  const user = await User.create({
    email,
    password,
    firstName,
    lastName,
    companyId: company.id,
    role: 'admin' // First user is admin
  });

  // Generate token
  const token = generateToken(user.id);

  // Update last login
  await User.updateLastLogin(user.id);

  res.status(201).json({
    message: 'Usuario y empresa registrados exitosamente',
    token,
    user: User.sanitizeUser({
      ...user,
      fullName: `${user.firstName} ${user.lastName}`,
      company: {
        id: company.id,
        name: company.name,
        email: company.contactEmail
      }
    })
  });
}));

// @route   POST /api/auth/login
// @desc    Login user
// @access  Public
router.post('/login', loginValidation, catchAsync(async (req, res) => {
  // Check validation errors
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Datos de login inválidos',
      details: errors.array()
    });
  }

  const { email, password } = req.body;

  // Find user by email
  const user = await User.findByEmail(email);

  if (!user || !(await User.comparePassword(user, password))) {
    throw new AppError('Email o contraseña incorrectos', 401, 'INVALID_CREDENTIALS');
  }

  if (!user.isActive) {
    throw new AppError('Tu cuenta ha sido desactivada', 401, 'ACCOUNT_DISABLED');
  }

  if (!user.company.isActive) {
    throw new AppError('La empresa asociada ha sido desactivada', 401, 'COMPANY_DISABLED');
  }

  // Generate token
  const token = generateToken(user.id);

  // Update last login
  await User.updateLastLogin(user.id);

  res.json({
    message: 'Login exitoso',
    token,
    user: User.sanitizeUser({
      ...user,
      fullName: `${user.firstName} ${user.lastName}`,
      company: {
        id: user.company.id,
        name: user.company.name,
        email: user.company.contactEmail,
        subscription: {
          plan: user.company.subscriptionPlan,
          status: user.company.subscriptionStatus,
          expiresAt: user.company.subscriptionExpiresAt,
          maxInvoicesPerMonth: user.company.subscriptionMaxInvoicesPerMonth
        }
      }
    })
  });
}));

// @route   GET /api/auth/me
// @desc    Get current user
// @access  Private
router.get('/me', authenticate, catchAsync(async (req, res) => {
  res.json({
    user: User.sanitizeUser({
      ...req.user,
      fullName: `${req.user.firstName} ${req.user.lastName}`,
      preferences: {
        language: req.user.preferenceLanguage,
        timezone: req.user.preferenceTimezone,
        currency: req.user.preferenceCurrency,
        dateFormat: req.user.preferenceDateFormat,
        notifications: {
          email: req.user.notificationEmail,
          invoiceCreated: req.user.notificationInvoice,
          paymentReceived: req.user.notificationPayment
        }
      },
      company: {
        id: req.company.id,
        name: req.company.name,
        email: req.company.contactEmail,
        subscription: {
          plan: req.company.subscriptionPlan,
          status: req.company.subscriptionStatus,
          expiresAt: req.company.subscriptionExpiresAt,
          maxInvoicesPerMonth: req.company.subscriptionMaxInvoicesPerMonth
        },
        taxSettings: {
          taxRates: req.company.taxRates
        },
        invoiceSettings: {
          prefix: req.company.invoicePrefix,
          numberLength: req.company.invoiceNumberLength,
          dueDays: req.company.dueDays
        }
      }
    })
  });
}));

// @route   POST /api/auth/generate-api-key
// @desc    Generate new API key
// @access  Private
router.post('/generate-api-key', authenticate, catchAsync(async (req, res) => {
  const apiKey = User.generateApiKey();
  await User.update(req.user.id, { apiKey });

  res.json({
    message: 'API Key generada exitosamente',
    apiKey,
    warning: 'Guarda esta API Key en un lugar seguro. No podrás verla de nuevo.'
  });
}));

// @route   DELETE /api/auth/revoke-api-key
// @desc    Revoke API key
// @access  Private
router.delete('/revoke-api-key', authenticate, catchAsync(async (req, res) => {
  await User.update(req.user.id, { apiKey: null });

  res.json({
    message: 'API Key revocada exitosamente'
  });
}));

// @route   POST /api/auth/refresh
// @desc    Refresh JWT token
// @access  Private
router.post('/refresh', authenticate, catchAsync(async (req, res) => {
  const token = generateToken(req.user._id);

  res.json({
    message: 'Token renovado exitosamente',
    token
  });
}));

module.exports = router;
