const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { Customer } = require('../models/prisma');
const { catchAsync, AppError } = require('../middleware/errorHandler');
const { authorize } = require('../middleware/auth');

const router = express.Router();

// Validation rules
const createCustomerValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('El nombre debe tener entre 2 y 200 caracteres'),
  body('customerType')
    .isIn(['individual', 'company'])
    .withMessage('Tipo de cliente inválido'),
  body('address.street')
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('La dirección debe tener entre 5 y 200 caracteres'),
  body('address.city')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('La ciudad debe tener entre 2 y 100 caracteres'),
  body('address.postalCode')
    .matches(/^[0-9]{5}$/)
    .withMessage('El código postal debe tener 5 dígitos'),
  body('contact.email')
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage('Email inválido'),
  body('contact.phone')
    .optional()
    .matches(/^(\+34|0034|34)?[6789][0-9]{8}$/)
    .withMessage('Número de teléfono español inválido'),
  body('cif')
    .optional()
    .matches(/^[ABCDEFGHJNPQRSUVW][0-9]{7}[0-9A-J]$/i)
    .withMessage('CIF inválido'),
  body('nif')
    .optional()
    .matches(/^[0-9]{8}[TRWAGMYFPDXBNJZSQVHLCKE]$/i)
    .withMessage('NIF inválido'),
  body('nie')
    .optional()
    .matches(/^[XYZ][0-9]{7}[TRWAGMYFPDXBNJZSQVHLCKE]$/i)
    .withMessage('NIE inválido')
];

// @route   GET /api/customers
// @desc    Get all customers for company
// @access  Private
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('Página debe ser un número mayor a 0'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Límite debe estar entre 1 y 100'),
  query('search').optional().trim().isLength({ min: 1 }).withMessage('Búsqueda debe tener al menos 1 caracter')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Parámetros de consulta inválidos',
      details: errors.array()
    });
  }

  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 20;

  const options = {
    page,
    limit,
    search: req.query.search,
    isActive: req.query.isActive !== undefined ? req.query.isActive === 'true' : undefined
  };

  const result = await Customer.findByCompany(req.company.id, options);

  res.json({
    customers: result.customers,
    total: result.total,
    page: result.page,
    pages: result.pages,
    pagination: {
      page: result.page,
      limit,
      total: result.total,
      pages: result.pages,
      hasNext: result.page < result.pages,
      hasPrev: result.page > 1
    }
  });
}));

// @route   GET /api/customers/:id
// @desc    Get single customer
// @access  Private
router.get('/:id', catchAsync(async (req, res) => {
  const customer = await Customer.findById(req.params.id);

  if (!customer || customer.companyId !== req.company.id) {
    throw new AppError('Cliente no encontrado', 404, 'CUSTOMER_NOT_FOUND');
  }

  res.json({ customer });
}));

// @route   POST /api/customers
// @desc    Create new customer
// @access  Private
router.post('/', createCustomerValidation, catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Datos de cliente inválidos',
      details: errors.array()
    });
  }

  // Check for duplicate tax ID within company
  if (req.body.cif || req.body.nif || req.body.nie) {
    const taxId = req.body.cif || req.body.nif || req.body.nie;
    const existingCustomer = await Customer.findOne({
      company: req.company._id,
      $or: [
        { cif: taxId },
        { nif: taxId },
        { nie: taxId }
      ]
    });

    if (existingCustomer) {
      throw new AppError('Ya existe un cliente con este identificador fiscal', 400, 'DUPLICATE_TAX_ID');
    }
  }

  const customer = new Customer({
    ...req.body,
    company: req.company._id
  });

  await customer.save();

  res.status(201).json({
    message: 'Cliente creado exitosamente',
    customer
  });
}));

// @route   PUT /api/customers/:id
// @desc    Update customer
// @access  Private
router.put('/:id', createCustomerValidation, catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Datos de actualización inválidos',
      details: errors.array()
    });
  }

  const customer = await Customer.findOne({
    _id: req.params.id,
    company: req.company._id
  });

  if (!customer) {
    throw new AppError('Cliente no encontrado', 404, 'CUSTOMER_NOT_FOUND');
  }

  // Check for duplicate tax ID within company (excluding current customer)
  if (req.body.cif || req.body.nif || req.body.nie) {
    const taxId = req.body.cif || req.body.nif || req.body.nie;
    const existingCustomer = await Customer.findOne({
      company: req.company._id,
      _id: { $ne: req.params.id },
      $or: [
        { cif: taxId },
        { nif: taxId },
        { nie: taxId }
      ]
    });

    if (existingCustomer) {
      throw new AppError('Ya existe otro cliente con este identificador fiscal', 400, 'DUPLICATE_TAX_ID');
    }
  }

  // Update customer
  Object.assign(customer, req.body);
  await customer.save();

  res.json({
    message: 'Cliente actualizado exitosamente',
    customer
  });
}));

// @route   DELETE /api/customers/:id
// @desc    Delete customer (soft delete)
// @access  Private
router.delete('/:id', authorize('admin'), catchAsync(async (req, res) => {
  const customer = await Customer.findOne({
    _id: req.params.id,
    company: req.company._id
  });

  if (!customer) {
    throw new AppError('Cliente no encontrado', 404, 'CUSTOMER_NOT_FOUND');
  }

  // Check if customer has invoices
  const Invoice = require('../models/Invoice');
  const invoiceCount = await Invoice.countDocuments({ customer: customer._id });

  if (invoiceCount > 0) {
    // Soft delete - just deactivate
    customer.isActive = false;
    await customer.save();
    
    res.json({
      message: 'Cliente desactivado exitosamente (tiene facturas asociadas)'
    });
  } else {
    // Hard delete if no invoices
    await Customer.findByIdAndDelete(req.params.id);
    
    res.json({
      message: 'Cliente eliminado exitosamente'
    });
  }
}));

// @route   PATCH /api/customers/:id/activate
// @desc    Activate/deactivate customer
// @access  Private
router.patch('/:id/activate', [
  body('isActive').isBoolean().withMessage('isActive debe ser true o false')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Datos inválidos',
      details: errors.array()
    });
  }

  const customer = await Customer.findOne({
    _id: req.params.id,
    company: req.company._id
  });

  if (!customer) {
    throw new AppError('Cliente no encontrado', 404, 'CUSTOMER_NOT_FOUND');
  }

  customer.isActive = req.body.isActive;
  await customer.save();

  res.json({
    message: `Cliente ${req.body.isActive ? 'activado' : 'desactivado'} exitosamente`,
    customer: {
      id: customer._id,
      name: customer.name,
      isActive: customer.isActive
    }
  });
}));

// @route   GET /api/customers/:id/invoices
// @desc    Get customer invoices
// @access  Private
router.get('/:id/invoices', catchAsync(async (req, res) => {
  const customer = await Customer.findOne({
    _id: req.params.id,
    company: req.company._id
  });

  if (!customer) {
    throw new AppError('Cliente no encontrado', 404, 'CUSTOMER_NOT_FOUND');
  }

  const Invoice = require('../models/Invoice');
  const invoices = await Invoice.find({ customer: customer._id })
    .sort({ issueDate: -1 })
    .select('invoiceNumber issueDate dueDate total status paymentInfo.status');

  res.json({
    customer: {
      id: customer._id,
      name: customer.displayName,
      stats: customer.stats
    },
    invoices
  });
}));

// @route   POST /api/customers/import
// @desc    Import customers from external source
// @access  Private
router.post('/import', authorize('admin'), [
  body('source').isIn(['woocommerce', 'shopify', 'prestashop']).withMessage('Fuente de importación inválida'),
  body('customers').isArray({ min: 1 }).withMessage('Debe proporcionar al menos un cliente')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Datos de importación inválidos',
      details: errors.array()
    });
  }

  const { source, customers } = req.body;
  const results = {
    imported: 0,
    updated: 0,
    errors: []
  };

  for (const customerData of customers) {
    try {
      // Check if customer already exists by external ID
      const externalIdField = `externalIds.${source}`;
      let existingCustomer = await Customer.findOne({
        company: req.company._id,
        [externalIdField]: customerData.externalId
      });

      if (existingCustomer) {
        // Update existing customer
        Object.assign(existingCustomer, {
          ...customerData,
          externalIds: {
            ...existingCustomer.externalIds,
            [source]: customerData.externalId
          }
        });
        await existingCustomer.save();
        results.updated++;
      } else {
        // Create new customer
        const newCustomer = new Customer({
          ...customerData,
          company: req.company._id,
          externalIds: {
            [source]: customerData.externalId
          }
        });
        await newCustomer.save();
        results.imported++;
      }
    } catch (error) {
      results.errors.push({
        customer: customerData.name || customerData.email,
        error: error.message
      });
    }
  }

  res.json({
    message: 'Importación completada',
    results
  });
}));

module.exports = router;
