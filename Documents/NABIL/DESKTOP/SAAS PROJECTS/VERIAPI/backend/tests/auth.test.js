const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../src/app');
const User = require('../src/models/User');
const Company = require('../src/models/Company');

describe('Authentication Endpoints', () => {
  beforeAll(async () => {
    // Connect to test database
    const mongoUri = process.env.TEST_DATABASE_URL || 'mongodb://localhost:27017/veriapi_test';
    await mongoose.connect(mongoUri);
  });

  beforeEach(async () => {
    // Clean database before each test
    await User.deleteMany({});
    await Company.deleteMany({});
  });

  afterAll(async () => {
    // Close database connection
    await mongoose.connection.close();
  });

  describe('POST /api/auth/register', () => {
    const validRegistrationData = {
      email: '<EMAIL>',
      password: 'password123',
      firstName: '<PERSON>',
      lastName: '<PERSON>',
      companyName: 'Test Empresa S.L.',
      companyEmail: '<EMAIL>',
      companyAddress: 'Calle Test 123',
      companyCity: 'Madrid',
      companyPostalCode: '28001',
      companyCif: 'B12345678'
    };

    it('should register a new user and company successfully', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send(validRegistrationData)
        .expect(201);

      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(validRegistrationData.email);
      expect(response.body.user.role).toBe('admin');
      expect(response.body.user.company.name).toBe(validRegistrationData.companyName);
    });

    it('should return 400 for invalid email', async () => {
      const invalidData = { ...validRegistrationData, email: 'invalid-email' };
      
      const response = await request(app)
        .post('/api/auth/register')
        .send(invalidData)
        .expect(400);

      expect(response.body).toHaveProperty('error');
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            msg: 'Debe ser un email válido'
          })
        ])
      );
    });

    it('should return 400 for short password', async () => {
      const invalidData = { ...validRegistrationData, password: '123' };
      
      const response = await request(app)
        .post('/api/auth/register')
        .send(invalidData)
        .expect(400);

      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            msg: 'La contraseña debe tener al menos 6 caracteres'
          })
        ])
      );
    });

    it('should return 400 for duplicate email', async () => {
      // First registration
      await request(app)
        .post('/api/auth/register')
        .send(validRegistrationData)
        .expect(201);

      // Second registration with same email
      const response = await request(app)
        .post('/api/auth/register')
        .send(validRegistrationData)
        .expect(400);

      expect(response.body.code).toBe('USER_EXISTS');
    });

    it('should return 400 for invalid postal code', async () => {
      const invalidData = { ...validRegistrationData, companyPostalCode: '123' };
      
      const response = await request(app)
        .post('/api/auth/register')
        .send(invalidData)
        .expect(400);

      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            msg: 'El código postal debe tener 5 dígitos'
          })
        ])
      );
    });
  });

  describe('POST /api/auth/login', () => {
    let userData;

    beforeEach(async () => {
      // Create a user for login tests
      userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Juan',
        lastName: 'Pérez',
        companyName: 'Test Empresa S.L.',
        companyEmail: '<EMAIL>',
        companyAddress: 'Calle Test 123',
        companyCity: 'Madrid',
        companyPostalCode: '28001',
        companyCif: 'B12345678'
      };

      await request(app)
        .post('/api/auth/register')
        .send(userData);
    });

    it('should login successfully with valid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: userData.email,
          password: userData.password
        })
        .expect(200);

      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(userData.email);
      expect(response.body.user).toHaveProperty('lastLogin');
    });

    it('should return 401 for invalid email', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: userData.password
        })
        .expect(401);

      expect(response.body.code).toBe('INVALID_CREDENTIALS');
    });

    it('should return 401 for invalid password', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: userData.email,
          password: 'wrongpassword'
        })
        .expect(401);

      expect(response.body.code).toBe('INVALID_CREDENTIALS');
    });

    it('should return 400 for missing email', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          password: userData.password
        })
        .expect(400);

      expect(response.body).toHaveProperty('details');
    });
  });

  describe('GET /api/auth/me', () => {
    let token;
    let userData;

    beforeEach(async () => {
      userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Juan',
        lastName: 'Pérez',
        companyName: 'Test Empresa S.L.',
        companyEmail: '<EMAIL>',
        companyAddress: 'Calle Test 123',
        companyCity: 'Madrid',
        companyPostalCode: '28001',
        companyCif: 'B12345678'
      };

      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send(userData);

      token = registerResponse.body.token;
    });

    it('should return user data with valid token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(userData.email);
      expect(response.body.user).toHaveProperty('company');
    });

    it('should return 401 without token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .expect(401);

      expect(response.body.code).toBe('TOKEN_REQUIRED');
    });

    it('should return 401 with invalid token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body.code).toBe('INVALID_TOKEN');
    });
  });

  describe('POST /api/auth/generate-api-key', () => {
    let token;

    beforeEach(async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'Juan',
        lastName: 'Pérez',
        companyName: 'Test Empresa S.L.',
        companyEmail: '<EMAIL>',
        companyAddress: 'Calle Test 123',
        companyCity: 'Madrid',
        companyPostalCode: '28001',
        companyCif: 'B12345678'
      };

      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send(userData);

      token = registerResponse.body.token;
    });

    it('should generate API key successfully', async () => {
      const response = await request(app)
        .post('/api/auth/generate-api-key')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(response.body).toHaveProperty('apiKey');
      expect(response.body.apiKey).toMatch(/^vapi_[a-f0-9]{40}$/);
      expect(response.body).toHaveProperty('warning');
    });

    it('should return 401 without authentication', async () => {
      const response = await request(app)
        .post('/api/auth/generate-api-key')
        .expect(401);

      expect(response.body.error).toBe('Token de acceso requerido');
    });
  });
});
