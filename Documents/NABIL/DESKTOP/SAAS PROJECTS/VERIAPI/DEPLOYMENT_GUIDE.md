# VeriAPI Deployment Guide

## Backend Deployment Options

### Option 1: Railway (Recommended)

1. **Connect Repository**
   - Go to [Railway](https://railway.app)
   - Connect your GitHub repository
   - Select the `backend` folder as the root

2. **Environment Variables**
   ```
   NODE_ENV=production
   DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require
   JWT_SECRET=your-super-secure-jwt-secret-here
   API_BASE_URL=https://your-app.railway.app
   FRONTEND_URL=https://app.soloun.link
   CORS_ORIGIN=https://app.soloun.link,https://www.soloun.link
   ```

3. **Custom Domain**
   - In Railway dashboard, go to Settings > Domains
   - Add custom domain: `api.soloun.link`
   - Update DNS records as instructed

### Option 2: Render

1. **Create Web Service**
   - Go to [Render](https://render.com)
   - Connect repository and select `backend` folder
   - Use the provided `render.yaml` configuration

2. **Environment Variables**
   - Set the same variables as Railway
   - Render will auto-generate JWT_SECRET

3. **Custom Domain**
   - In Render dashboard, add custom domain `api.soloun.link`

### Option 3: Docker Deployment (VPS)

1. **Build and Deploy**
   ```bash
   # Build the image
   docker build -t veriapi-backend .
   
   # Run with environment variables
   docker run -d \
     --name veriapi \
     -p 3000:3000 \
     -e NODE_ENV=production \
     -e DATABASE_URL="your-neon-url" \
     -e JWT_SECRET="your-jwt-secret" \
     veriapi-backend
   ```

2. **Nginx Configuration**
   ```nginx
   server {
       listen 80;
       server_name api.soloun.link;
       
       location / {
           proxy_pass http://localhost:3000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
   }
   ```

## Frontend Deployment (Vercel)

1. **Connect Repository**
   - Go to [Vercel](https://vercel.com)
   - Import your repository
   - Set root directory to `dashboard`

2. **Environment Variables**
   ```
   NEXT_PUBLIC_API_URL=https://api.soloun.link
   NEXT_PUBLIC_APP_URL=https://app.soloun.link
   ```

3. **Custom Domain**
   - In Vercel dashboard, add domain `app.soloun.link`
   - Configure DNS as instructed

## DNS Configuration

### For Cloudflare or your DNS provider:

```
Type: CNAME
Name: api
Target: your-railway-app.railway.app (or render URL)

Type: CNAME  
Name: app
Target: your-vercel-app.vercel.app
```

## Post-Deployment Steps

1. **Test API Health**
   ```bash
   curl https://api.soloun.link/health
   ```

2. **Create First Company and User**
   ```bash
   curl -X POST https://api.soloun.link/api/auth/register \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "password": "SecurePassword123",
       "firstName": "Admin",
       "lastName": "User",
       "companyName": "Soloun S.L.",
       "companyEmail": "<EMAIL>",
       "companyAddress": "Calle Principal 123",
       "companyCity": "Madrid",
       "companyPostalCode": "28001",
       "companyPhone": "+34 91 123 45 67",
       "companyCif": "B12345678"
     }'
   ```

3. **Test Frontend**
   - Visit `https://app.soloun.link`
   - Login with the created credentials
   - Verify dashboard functionality

## Monitoring

- **Health Check**: `https://api.soloun.link/health`
- **Logs**: Check Railway/Render dashboard
- **Database**: Monitor Neon.tech dashboard

## Security Checklist

- [ ] JWT_SECRET is secure and unique
- [ ] CORS is properly configured
- [ ] HTTPS is enabled
- [ ] Rate limiting is active
- [ ] Database connection is encrypted
- [ ] Environment variables are secure
