"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: function() { return /* binding */ authApi; },\n/* harmony export */   companyApi: function() { return /* binding */ companyApi; },\n/* harmony export */   customersApi: function() { return /* binding */ customersApi; },\n/* harmony export */   invoicesApi: function() { return /* binding */ invoicesApi; }\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(app-pages-browser)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n// API Configuration\nconst API_BASE_URL = \"http://localhost:3000/api\" || 0;\nconst TOKEN_KEY = \"veriapi_token\";\n// Create axios instance\nconst createApiClient = ()=>{\n    const client = axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n        baseURL: API_BASE_URL,\n        timeout: 30000,\n        headers: {\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    // Request interceptor to add auth token\n    client.interceptors.request.use((config)=>{\n        var _config_method;\n        console.log(\"API Request:\", (_config_method = config.method) === null || _config_method === void 0 ? void 0 : _config_method.toUpperCase(), config.url, config.data);\n        const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(TOKEN_KEY);\n        if (token) {\n            config.headers.Authorization = \"Bearer \".concat(token);\n        }\n        return config;\n    }, (error)=>{\n        console.error(\"Request interceptor error:\", error);\n        return Promise.reject(error);\n    });\n    // Response interceptor for error handling\n    client.interceptors.response.use((response)=>response, (error)=>{\n        var _error_response_data, _error_response, _error_response1, _error_response2;\n        const message = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || error.message || \"An error occurred\";\n        if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 401) {\n            // Unauthorized - clear token and redirect to login\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(TOKEN_KEY);\n            if (true) {\n                window.location.href = \"/login\";\n            }\n        } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) >= 500) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Server error. Please try again later.\");\n        } else {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.error(message);\n        }\n        return Promise.reject(error);\n    });\n    return client;\n};\nconst api = createApiClient();\n// Auth API\nconst authApi = {\n    login: async (credentials)=>{\n        const response = await api.post(\"/auth/login\", credentials);\n        // Store token in cookie\n        if (response.data.token) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(TOKEN_KEY, response.data.token, {\n                expires: 7,\n                secure: \"development\" === \"production\",\n                sameSite: \"strict\"\n            });\n        }\n        return response.data;\n    },\n    register: async (data)=>{\n        const response = await api.post(\"/auth/register\", data);\n        // Store token in cookie\n        if (response.data.token) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(TOKEN_KEY, response.data.token, {\n                expires: 7,\n                secure: \"development\" === \"production\",\n                sameSite: \"strict\"\n            });\n        }\n        return response.data;\n    },\n    logout: ()=>{\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(TOKEN_KEY);\n        if (true) {\n            window.location.href = \"/login\";\n        }\n    },\n    getToken: ()=>{\n        return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(TOKEN_KEY);\n    },\n    isAuthenticated: ()=>{\n        return !!js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(TOKEN_KEY);\n    }\n};\n// Company API\nconst companyApi = {\n    getCompany: async ()=>{\n        const response = await api.get(\"/company\");\n        return response.data.company;\n    },\n    updateCompany: async (data)=>{\n        const response = await api.put(\"/company\", data);\n        return response.data.company;\n    },\n    getStats: async ()=>{\n        const response = await api.get(\"/company/stats\");\n        return response.data.stats;\n    },\n    getNextInvoiceNumber: async ()=>{\n        const response = await api.get(\"/company/next-invoice-number\");\n        return response.data;\n    }\n};\n// Customers API\nconst customersApi = {\n    getCustomers: async (filters)=>{\n        const params = new URLSearchParams();\n        if (filters === null || filters === void 0 ? void 0 : filters.page) params.append(\"page\", filters.page.toString());\n        if (filters === null || filters === void 0 ? void 0 : filters.limit) params.append(\"limit\", filters.limit.toString());\n        if (filters === null || filters === void 0 ? void 0 : filters.search) params.append(\"search\", filters.search);\n        if (filters === null || filters === void 0 ? void 0 : filters.customerType) params.append(\"customerType\", filters.customerType);\n        if ((filters === null || filters === void 0 ? void 0 : filters.isActive) !== undefined) params.append(\"isActive\", filters.isActive.toString());\n        const response = await api.get(\"/customers?\".concat(params.toString()));\n        return response.data;\n    },\n    getCustomer: async (id)=>{\n        const response = await api.get(\"/customers/\".concat(id));\n        return response.data.customer;\n    },\n    createCustomer: async (data)=>{\n        const response = await api.post(\"/customers\", data);\n        return response.data.customer;\n    },\n    updateCustomer: async (id, data)=>{\n        const response = await api.put(\"/customers/\".concat(id), data);\n        return response.data.customer;\n    },\n    deleteCustomer: async (id)=>{\n        await api.delete(\"/customers/\".concat(id));\n    }\n};\n// Invoices API\nconst invoicesApi = {\n    getInvoices: async (filters)=>{\n        const params = new URLSearchParams();\n        if (filters === null || filters === void 0 ? void 0 : filters.page) params.append(\"page\", filters.page.toString());\n        if (filters === null || filters === void 0 ? void 0 : filters.limit) params.append(\"limit\", filters.limit.toString());\n        if (filters === null || filters === void 0 ? void 0 : filters.status) params.append(\"status\", filters.status);\n        if (filters === null || filters === void 0 ? void 0 : filters.customer) params.append(\"customer\", filters.customer);\n        if (filters === null || filters === void 0 ? void 0 : filters.dateFrom) params.append(\"dateFrom\", filters.dateFrom);\n        if (filters === null || filters === void 0 ? void 0 : filters.dateTo) params.append(\"dateTo\", filters.dateTo);\n        if (filters === null || filters === void 0 ? void 0 : filters.platform) params.append(\"platform\", filters.platform);\n        if (filters === null || filters === void 0 ? void 0 : filters.search) params.append(\"search\", filters.search);\n        const response = await api.get(\"/invoices?\".concat(params.toString()));\n        return response.data;\n    },\n    getInvoice: async (id)=>{\n        const response = await api.get(\"/invoices/\".concat(id));\n        return response.data.invoice;\n    },\n    createInvoice: async (data)=>{\n        const response = await api.post(\"/invoices\", data);\n        return response.data.invoice;\n    },\n    updateInvoice: async (id, data)=>{\n        const response = await api.put(\"/invoices/\".concat(id), data);\n        return response.data.invoice;\n    },\n    deleteInvoice: async (id)=>{\n        await api.delete(\"/invoices/\".concat(id));\n    },\n    downloadPdf: async (id)=>{\n        const response = await api.get(\"/invoices/\".concat(id, \"/pdf\"), {\n            responseType: \"blob\"\n        });\n        return response.data;\n    },\n    recordPayment: async (id, payment)=>{\n        const response = await api.post(\"/invoices/\".concat(id, \"/payment\"), payment);\n        return response.data.invoice;\n    }\n};\n// Export the main API client for custom requests\n/* harmony default export */ __webpack_exports__[\"default\"] = (api);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api.ts\n"));

/***/ })

});