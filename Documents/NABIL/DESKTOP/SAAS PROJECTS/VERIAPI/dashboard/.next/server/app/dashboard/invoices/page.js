/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/invoices/page";
exports.ids = ["app/dashboard/invoices/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Finvoices%2Fpage&page=%2Fdashboard%2Finvoices%2Fpage&appPaths=%2Fdashboard%2Finvoices%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Finvoices%2Fpage.tsx&appDir=%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Finvoices%2Fpage&page=%2Fdashboard%2Finvoices%2Fpage&appPaths=%2Fdashboard%2Finvoices%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Finvoices%2Fpage.tsx&appDir=%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: [\n        'invoices',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/invoices/page.tsx */ \"(rsc)/./src/app/dashboard/invoices/page.tsx\")), \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/invoices/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/invoices/page\",\n        pathname: \"/dashboard/invoices\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZkYXNoYm9hcmQlMkZpbnZvaWNlcyUyRnBhZ2UmcGFnZT0lMkZkYXNoYm9hcmQlMkZpbnZvaWNlcyUyRnBhZ2UmYXBwUGF0aHM9JTJGZGFzaGJvYXJkJTJGaW52b2ljZXMlMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGZGFzaGJvYXJkJTJGaW52b2ljZXMlMkZwYWdlLnRzeCZhcHBEaXI9JTJGVXNlcnMlMkZtYWNuYWJpbCUyRkRvY3VtZW50cyUyRk5BQklMJTJGREVTS1RPUCUyRlNBQVMlMjBQUk9KRUNUUyUyRlZFUklBUEklMkZkYXNoYm9hcmQlMkZzcmMlMkZhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPSUyRlVzZXJzJTJGbWFjbmFiaWwlMkZEb2N1bWVudHMlMkZOQUJJTCUyRkRFU0tUT1AlMkZTQUFTJTIwUFJPSkVDVFMlMkZWRVJJQVBJJTJGZGFzaGJvYXJkJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsYUFBYSxzQkFBc0I7QUFDaUU7QUFDckM7QUFDL0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDLHVCQUF1QixzTEFBZ0o7QUFDdks7QUFDQSxTQUFTO0FBQ1QsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBLHlCQUF5QixvSkFBK0g7QUFDeEosb0JBQW9CLDBOQUFnRjtBQUNwRztBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDdUI7QUFDNkQ7QUFDcEYsNkJBQTZCLG1CQUFtQjtBQUNoRDtBQUNPO0FBQ0E7QUFDUDtBQUNBO0FBQ0E7QUFDdUQ7QUFDdkQ7QUFDTyx3QkFBd0IsOEdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsid2VicGFjazovL3ZlcmlhcGktZGFzaGJvYXJkLz82NWQ3Il0sInNvdXJjZXNDb250ZW50IjpbIlwiVFVSQk9QQUNLIHsgdHJhbnNpdGlvbjogbmV4dC1zc3IgfVwiO1xuaW1wb3J0IHsgQXBwUGFnZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuLy8gV2UgaW5qZWN0IHRoZSB0cmVlIGFuZCBwYWdlcyBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgdHJlZSA9IHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJycsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJ2Rhc2hib2FyZCcsXG4gICAgICAgIHtcbiAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgJ2ludm9pY2VzJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL21hY25hYmlsL0RvY3VtZW50cy9OQUJJTC9ERVNLVE9QL1NBQVMgUFJPSkVDVFMvVkVSSUFQSS9kYXNoYm9hcmQvc3JjL2FwcC9kYXNoYm9hcmQvaW52b2ljZXMvcGFnZS50c3hcIiksIFwiL1VzZXJzL21hY25hYmlsL0RvY3VtZW50cy9OQUJJTC9ERVNLVE9QL1NBQVMgUFJPSkVDVFMvVkVSSUFQSS9kYXNoYm9hcmQvc3JjL2FwcC9kYXNoYm9hcmQvaW52b2ljZXMvcGFnZS50c3hcIl0sXG4gICAgICAgICAgXG4gICAgICAgIH1dXG4gICAgICB9LFxuICAgICAgICB7XG4gICAgICAgIFxuICAgICAgICBcbiAgICAgIH1cbiAgICAgIF1cbiAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogWygpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL21hY25hYmlsL0RvY3VtZW50cy9OQUJJTC9ERVNLVE9QL1NBQVMgUFJPSkVDVFMvVkVSSUFQSS9kYXNoYm9hcmQvc3JjL2FwcC9sYXlvdXQudHN4XCIpLCBcIi9Vc2Vycy9tYWNuYWJpbC9Eb2N1bWVudHMvTkFCSUwvREVTS1RPUC9TQUFTIFBST0pFQ1RTL1ZFUklBUEkvZGFzaGJvYXJkL3NyYy9hcHAvbGF5b3V0LnRzeFwiXSxcbidub3QtZm91bmQnOiBbKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm90LWZvdW5kLWVycm9yXCIpLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9ub3QtZm91bmQtZXJyb3JcIl0sXG4gICAgICAgIFxuICAgICAgfVxuICAgICAgXVxuICAgICAgfS5jaGlsZHJlbjtcbmNvbnN0IHBhZ2VzID0gW1wiL1VzZXJzL21hY25hYmlsL0RvY3VtZW50cy9OQUJJTC9ERVNLVE9QL1NBQVMgUFJPSkVDVFMvVkVSSUFQSS9kYXNoYm9hcmQvc3JjL2FwcC9kYXNoYm9hcmQvaW52b2ljZXMvcGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIjtcbmNvbnN0IF9fbmV4dF9hcHBfcmVxdWlyZV9fID0gX193ZWJwYWNrX3JlcXVpcmVfX1xuY29uc3QgX19uZXh0X2FwcF9sb2FkX2NodW5rX18gPSAoKSA9PiBQcm9taXNlLnJlc29sdmUoKVxuZXhwb3J0IGNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9kYXNoYm9hcmQvaW52b2ljZXMvcGFnZVwiO1xuZXhwb3J0IGNvbnN0IF9fbmV4dF9hcHBfXyA9IHtcbiAgICByZXF1aXJlOiBfX25leHRfYXBwX3JlcXVpcmVfXyxcbiAgICBsb2FkQ2h1bms6IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fXG59O1xuZXhwb3J0ICogZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvYXBwLXJlbmRlci9lbnRyeS1iYXNlXCI7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBBcHBQYWdlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9QQUdFLFxuICAgICAgICBwYWdlOiBcIi9kYXNoYm9hcmQvaW52b2ljZXMvcGFnZVwiLFxuICAgICAgICBwYXRobmFtZTogXCIvZGFzaGJvYXJkL2ludm9pY2VzXCIsXG4gICAgICAgIC8vIFRoZSBmb2xsb3dpbmcgYXJlbid0IHVzZWQgaW4gcHJvZHVjdGlvbi5cbiAgICAgICAgYnVuZGxlUGF0aDogXCJcIixcbiAgICAgICAgZmlsZW5hbWU6IFwiXCIsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Finvoices%2Fpage&page=%2Fdashboard%2Finvoices%2Fpage&appPaths=%2Fdashboard%2Finvoices%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Finvoices%2Fpage.tsx&appDir=%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fhooks%2FuseAuth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fhooks%2FuseAuth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/hooks/useAuth.tsx */ \"(ssr)/./src/hooks/useAuth.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fnode_modules%2Freact-hot-toast%2Fdist%2Findex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fhooks%2FuseAuth.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fapp%2Fdashboard%2Finvoices%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fapp%2Fdashboard%2Finvoices%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/invoices/page.tsx */ \"(ssr)/./src/app/dashboard/invoices/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbWFjbmFiaWwlMkZEb2N1bWVudHMlMkZOQUJJTCUyRkRFU0tUT1AlMkZTQUFTJTIwUFJPSkVDVFMlMkZWRVJJQVBJJTJGZGFzaGJvYXJkJTJGc3JjJTJGYXBwJTJGZGFzaGJvYXJkJTJGaW52b2ljZXMlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc0xBQWdKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdmVyaWFwaS1kYXNoYm9hcmQvPzNlNzYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbWFjbmFiaWwvRG9jdW1lbnRzL05BQklML0RFU0tUT1AvU0FBUyBQUk9KRUNUUy9WRVJJQVBJL2Rhc2hib2FyZC9zcmMvYXBwL2Rhc2hib2FyZC9pbnZvaWNlcy9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fapp%2Fdashboard%2Finvoices%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/invoices/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/dashboard/invoices/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InvoicesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/DashboardLayout */ \"(ssr)/./src/components/layout/DashboardLayout.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/Badge */ \"(ssr)/./src/components/ui/Badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,Download,Edit,Euro,Eye,FileText,MoreHorizontal,Plus,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,Download,Edit,Euro,Eye,FileText,MoreHorizontal,Plus,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/euro.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,Download,Edit,Euro,Eye,FileText,MoreHorizontal,Plus,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,Download,Edit,Euro,Eye,FileText,MoreHorizontal,Plus,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,Download,Edit,Euro,Eye,FileText,MoreHorizontal,Plus,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,Download,Edit,Euro,Eye,FileText,MoreHorizontal,Plus,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,Download,Edit,Euro,Eye,FileText,MoreHorizontal,Plus,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,Download,Edit,Euro,Eye,FileText,MoreHorizontal,Plus,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,Download,Edit,Euro,Eye,FileText,MoreHorizontal,Plus,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,Download,Edit,Euro,Eye,FileText,MoreHorizontal,Plus,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,Download,Edit,Euro,Eye,FileText,MoreHorizontal,Plus,Search,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// Mock data for demonstration\nconst mockInvoices = [\n    {\n        id: \"1\",\n        invoiceNumber: \"FAC-000156\",\n        customer: {\n            id: \"1\",\n            name: \"Empresa ABC S.L.\",\n            customerType: \"company\"\n        },\n        issueDate: \"2024-01-15\",\n        dueDate: \"2024-02-14\",\n        total: 1250.50,\n        status: \"paid\",\n        externalReferences: {\n            platform: \"woocommerce\",\n            externalId: \"WC-12345\"\n        }\n    },\n    {\n        id: \"2\",\n        invoiceNumber: \"FAC-000155\",\n        customer: {\n            id: \"2\",\n            name: \"Juan Garc\\xeda\",\n            customerType: \"individual\"\n        },\n        issueDate: \"2024-01-14\",\n        dueDate: \"2024-02-13\",\n        total: 850.00,\n        status: \"sent\",\n        externalReferences: null\n    },\n    {\n        id: \"3\",\n        invoiceNumber: \"FAC-000154\",\n        customer: {\n            id: \"3\",\n            name: \"Tienda Online S.L.\",\n            customerType: \"company\"\n        },\n        issueDate: \"2024-01-10\",\n        dueDate: \"2024-02-09\",\n        total: 2100.75,\n        status: \"overdue\",\n        externalReferences: {\n            platform: \"shopify\",\n            externalId: \"SH-67890\"\n        }\n    },\n    {\n        id: \"4\",\n        invoiceNumber: \"FAC-000153\",\n        customer: {\n            id: \"4\",\n            name: \"Mar\\xeda L\\xf3pez\",\n            customerType: \"individual\"\n        },\n        issueDate: \"2024-01-08\",\n        dueDate: \"2024-02-07\",\n        total: 450.25,\n        status: \"draft\",\n        externalReferences: null\n    },\n    {\n        id: \"5\",\n        invoiceNumber: \"FAC-000152\",\n        customer: {\n            id: \"5\",\n            name: \"Consultora XYZ\",\n            customerType: \"company\"\n        },\n        issueDate: \"2024-01-05\",\n        dueDate: \"2024-02-04\",\n        total: 3200.00,\n        status: \"viewed\",\n        externalReferences: null\n    }\n];\nconst statusLabels = {\n    draft: \"Borrador\",\n    sent: \"Enviada\",\n    viewed: \"Vista\",\n    paid: \"Pagada\",\n    overdue: \"Vencida\",\n    cancelled: \"Cancelada\"\n};\nconst platformLabels = {\n    woocommerce: \"WooCommerce\",\n    shopify: \"Shopify\",\n    prestashop: \"PrestaShop\",\n    manual: \"Manual\"\n};\nfunction InvoicesPage() {\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [platformFilter, setPlatformFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    // Filter invoices based on search and filters\n    const filteredInvoices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return mockInvoices.filter((invoice)=>{\n            const matchesSearch = invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) || invoice.customer.name.toLowerCase().includes(searchTerm.toLowerCase());\n            const matchesStatus = statusFilter === \"all\" || invoice.status === statusFilter;\n            const matchesPlatform = platformFilter === \"all\" || platformFilter === \"manual\" && !invoice.externalReferences || invoice.externalReferences?.platform === platformFilter;\n            return matchesSearch && matchesStatus && matchesPlatform;\n        });\n    }, [\n        searchTerm,\n        statusFilter,\n        platformFilter\n    ]);\n    // Calculate summary stats\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const total = filteredInvoices.reduce((sum, inv)=>sum + inv.total, 0);\n        const paid = filteredInvoices.filter((inv)=>inv.status === \"paid\").reduce((sum, inv)=>sum + inv.total, 0);\n        const pending = total - paid;\n        return {\n            total: filteredInvoices.length,\n            totalAmount: total,\n            paidAmount: paid,\n            pendingAmount: pending\n        };\n    }, [\n        filteredInvoices\n    ]);\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"paid\":\n                return \"✓\";\n            case \"sent\":\n                return \"\\uD83D\\uDCE7\";\n            case \"viewed\":\n                return \"\\uD83D\\uDC41\";\n            case \"overdue\":\n                return \"⚠\";\n            case \"draft\":\n                return \"\\uD83D\\uDCDD\";\n            default:\n                return \"\\uD83D\\uDCC4\";\n        }\n    };\n    const getPlatformIcon = (platform)=>{\n        if (!platform) return \"✏️\";\n        switch(platform){\n            case \"woocommerce\":\n                return \"\\uD83D\\uDED2\";\n            case \"shopify\":\n                return \"\\uD83C\\uDFEA\";\n            case \"prestashop\":\n                return \"\\uD83D\\uDECD\";\n            default:\n                return \"\\uD83D\\uDD17\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DashboardLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        title: \"Facturas\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Total Facturas\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: stats.total\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-8 w-8 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Importe Total\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(stats.totalAmount)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-8 w-8 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Pagado\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(stats.paidAmount)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 w-8 bg-green-100 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-600 font-bold\",\n                                                children: \"✓\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-600\",\n                                                    children: \"Pendiente\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-orange-600\",\n                                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(stats.pendingAmount)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 w-8 bg-orange-100 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-orange-600 font-bold\",\n                                                children: \"⏳\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Lista de Facturas\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Nueva Factura\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Buscar por n\\xfamero de factura o cliente...\",\n                                                    className: \"input pl-10 w-full\",\n                                                    value: searchTerm,\n                                                    onChange: (e)=>setSearchTerm(e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            className: \"input w-full sm:w-auto\",\n                                            value: statusFilter,\n                                            onChange: (e)=>setStatusFilter(e.target.value),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"Todos los estados\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"draft\",\n                                                    children: \"Borrador\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"sent\",\n                                                    children: \"Enviada\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"viewed\",\n                                                    children: \"Vista\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 281,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"paid\",\n                                                    children: \"Pagada\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 282,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"overdue\",\n                                                    children: \"Vencida\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            className: \"input w-full sm:w-auto\",\n                                            value: platformFilter,\n                                            onChange: (e)=>setPlatformFilter(e.target.value),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"all\",\n                                                    children: \"Todas las plataformas\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"manual\",\n                                                    children: \"Manual\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 292,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"woocommerce\",\n                                                    children: \"WooCommerce\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"shopify\",\n                                                    children: \"Shopify\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"prestashop\",\n                                                    children: \"PrestaShop\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"table\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                className: \"table-header\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"table-row\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"table-head\",\n                                                            children: \"Factura\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"table-head\",\n                                                            children: \"Cliente\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"table-head\",\n                                                            children: \"Fecha\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"table-head\",\n                                                            children: \"Vencimiento\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"table-head\",\n                                                            children: \"Importe\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"table-head\",\n                                                            children: \"Estado\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"table-head\",\n                                                            children: \"Origen\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            className: \"table-head\",\n                                                            children: \"Acciones\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                children: filteredInvoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"table-row\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"table-cell\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-gray-400 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                            lineNumber: 319,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: invoice.invoiceNumber\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                            lineNumber: 320,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                    lineNumber: 318,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                lineNumber: 317,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"table-cell\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        invoice.customer.customerType === \"company\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-gray-400 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                            lineNumber: 326,\n                                                                            columnNumber: 29\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-gray-400 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                            lineNumber: 328,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: invoice.customer.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                            lineNumber: 330,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                lineNumber: 323,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"table-cell\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-gray-400 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                            lineNumber: 335,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(invoice.issueDate)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"table-cell\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatDate)(invoice.dueDate)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"table-cell font-medium\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(invoice.total)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                lineNumber: 340,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"table-cell\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Badge__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getInvoiceStatusColor)(invoice.status),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"mr-1\",\n                                                                            children: getStatusIcon(invoice.status)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                            lineNumber: 343,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        statusLabels[invoice.status]\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                    lineNumber: 342,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"table-cell\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"mr-1\",\n                                                                            children: getPlatformIcon(invoice.externalReferences?.platform)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                            lineNumber: 349,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm\",\n                                                                            children: invoice.externalReferences?.platform ? platformLabels[invoice.externalReferences.platform] : \"Manual\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                            lineNumber: 352,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"table-cell\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            variant: \"ghost\",\n                                                                            size: \"sm\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                                lineNumber: 363,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                            lineNumber: 362,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            variant: \"ghost\",\n                                                                            size: \"sm\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                                lineNumber: 366,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                            lineNumber: 365,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            variant: \"ghost\",\n                                                                            size: \"sm\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                                lineNumber: 369,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                            lineNumber: 368,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            variant: \"ghost\",\n                                                                            size: \"sm\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                                lineNumber: 372,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                            lineNumber: 371,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                    lineNumber: 361,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, invoice.id, true, {\n                                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this),\n                                filteredInvoices.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-12\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900 mb-2\",\n                                            children: \"No se encontraron facturas\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 mb-4\",\n                                            children: searchTerm || statusFilter !== \"all\" || platformFilter !== \"all\" ? \"Intenta ajustar los filtros de b\\xfasqueda\" : \"Crea tu primera factura para comenzar\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_Download_Edit_Euro_Eye_FileText_MoreHorizontal_Plus_Search_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Nueva Factura\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n            lineNumber: 189,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx\",\n        lineNumber: 188,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/invoices/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/DashboardLayout.tsx":
/*!***************************************************!*\
  !*** ./src/components/layout/DashboardLayout.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ \"(ssr)/./src/components/layout/Sidebar.tsx\");\n/* harmony import */ var _Header__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./Header */ \"(ssr)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction DashboardLayout({ children, title, className }) {\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isAuthenticated, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useRequireAuth)();\n    // Show loading spinner while checking authentication\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"spinner w-8 h-8 border-blue-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/DashboardLayout.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Cargando...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/DashboardLayout.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/DashboardLayout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/DashboardLayout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render if not authenticated (useRequireAuth will redirect)\n    if (!isAuthenticated) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-screen flex overflow-hidden bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/DashboardLayout.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col flex-1 overflow-hidden lg:pl-64\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Header__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        title: title,\n                        onMobileMenuToggle: ()=>setIsMobileMenuOpen(!isMobileMenuOpen)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/DashboardLayout.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex-1 overflow-y-auto focus:outline-none\", className),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"py-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/DashboardLayout.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/DashboardLayout.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/DashboardLayout.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/DashboardLayout.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/DashboardLayout.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/DashboardLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,LogOut,Menu,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,LogOut,Menu,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,LogOut,Menu,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,LogOut,Menu,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,LogOut,Menu,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,LogOut,Menu,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Globe_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Globe,LogOut,Menu,Search,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n// import { useTranslations } from 'next-intl';\n\n\n\nfunction Header({ onMobileMenuToggle, title, className }) {\n    const [isProfileMenuOpen, setIsProfileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isNotificationsOpen, setIsNotificationsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user, logout } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    // const t = useTranslations();\n    // Mock notifications for demo\n    const notifications = [\n        {\n            id: 1,\n            title: \"Nueva factura creada\",\n            message: \"Factura FAC-000123 ha sido generada\",\n            time: \"5 min\",\n            unread: true\n        },\n        {\n            id: 2,\n            title: \"Pago recibido\",\n            message: \"Cliente Juan Garc\\xeda ha pagado €1,250.00\",\n            time: \"1 hora\",\n            unread: true\n        },\n        {\n            id: 3,\n            title: \"Integraci\\xf3n sincronizada\",\n            message: \"WooCommerce: 3 nuevos pedidos importados\",\n            time: \"2 horas\",\n            unread: false\n        }\n    ];\n    const unreadCount = notifications.filter((n)=>n.unread).length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"bg-white shadow-sm border-b border-gray-200\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                className: \"lg:hidden -ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n                                onClick: onMobileMenuToggle,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-6 w-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this),\n                            title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-4 lg:ml-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-semibold text-gray-900\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                lineNumber: 76,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Buscar\",\n                                            className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md\",\n                                    title: \"Cambiar idioma\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-md\",\n                                        onClick: ()=>setIsNotificationsOpen(!isNotificationsOpen),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 15\n                                            }, this),\n                                            unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"absolute -top-0.5 -right-0.5 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\",\n                                                children: unreadCount\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                lineNumber: 106,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this),\n                                    isNotificationsOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-medium text-gray-900 mb-3\",\n                                                    children: \"Notificaciones\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"p-3 rounded-md cursor-pointer hover:bg-gray-50\", notification.unread ? \"bg-blue-50 border-l-4 border-blue-500\" : \"bg-gray-50\"),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm font-medium text-gray-900\",\n                                                                                children: notification.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                                                lineNumber: 128,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-gray-500 mt-1\",\n                                                                                children: notification.message\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                                                lineNumber: 131,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                                        lineNumber: 127,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-gray-400 ml-2\",\n                                                                        children: notification.time\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                                        lineNumber: 135,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                                lineNumber: 126,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, notification.id, false, {\n                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 pt-3 border-t border-gray-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-sm text-blue-600 hover:text-blue-500\",\n                                                        children: \"Ver todas las notificaciones\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n                                        onClick: ()=>setIsProfileMenuOpen(!isProfileMenuOpen),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-white\",\n                                                children: user?.firstName?.charAt(0) || \"U\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this),\n                                    isProfileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"py-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"px-4 py-2 border-b border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: user?.fullName || \"Usuario\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: user?.email\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Perfil\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Configuraci\\xf3n\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-t border-gray-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: logout,\n                                                        className: \"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Globe_LogOut_Menu_Search_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Cerrar Sesi\\xf3n\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                        lineNumber: 188,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            (isProfileMenuOpen || isNotificationsOpen) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-40\",\n                onClick: ()=>{\n                    setIsProfileMenuOpen(false);\n                    setIsNotificationsOpen(false);\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n                lineNumber: 205,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Header.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,LogOut,Menu,Plug,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,LogOut,Menu,Plug,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,LogOut,Menu,Plug,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,LogOut,Menu,Plug,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plug.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,LogOut,Menu,Plug,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,LogOut,Menu,Plug,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,LogOut,Menu,Plug,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,LogOut,Menu,Plug,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,LogOut,Menu,Plug,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,LogOut,Menu,Plug,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,LogOut,Menu,Plug,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,ChevronDown,ChevronRight,CreditCard,FileText,LayoutDashboard,LogOut,Menu,Plug,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n// import { useTranslations } from 'next-intl';\n\n\n\nfunction Sidebar({ className }) {\n    const [isMobileMenuOpen, setIsMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    // const t = useTranslations();\n    const { logout, user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const navigation = [\n        {\n            name: \"Panel\",\n            href: \"/dashboard\",\n            icon: _barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n        },\n        {\n            name: \"Facturas\",\n            href: \"/dashboard/invoices\",\n            icon: _barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n        },\n        {\n            name: \"Clientes\",\n            href: \"/dashboard/customers\",\n            icon: _barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            name: \"Integraciones\",\n            href: \"/dashboard/integrations\",\n            icon: _barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            name: \"Suscripci\\xf3n\",\n            href: \"/dashboard/subscription\",\n            icon: _barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            name: \"Configuraci\\xf3n\",\n            href: \"/dashboard/settings\",\n            icon: _barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            children: [\n                {\n                    name: \"Empresa\",\n                    href: \"/dashboard/settings/company\",\n                    icon: _barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                },\n                {\n                    name: \"Perfil\",\n                    href: \"/dashboard/settings/profile\",\n                    icon: _barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                }\n            ]\n        }\n    ];\n    const toggleExpanded = (itemName)=>{\n        setExpandedItems((prev)=>prev.includes(itemName) ? prev.filter((name)=>name !== itemName) : [\n                ...prev,\n                itemName\n            ]);\n    };\n    const isCurrentPath = (href)=>{\n        return pathname === href || pathname.startsWith(href + \"/\");\n    };\n    const renderNavigationItem = (item, level = 0)=>{\n        const isExpanded = expandedItems.includes(item.name);\n        const isCurrent = isCurrentPath(item.href);\n        const hasChildren = item.children && item.children.length > 0;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: hasChildren ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>toggleExpanded(item.name),\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"group flex w-full items-center rounded-md px-2 py-2 text-sm font-medium transition-colors\", level > 0 ? \"pl-8\" : \"\", isCurrent ? \"bg-blue-100 text-blue-900\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"mr-3 h-5 w-5 flex-shrink-0\", isCurrent ? \"text-blue-500\" : \"text-gray-400 group-hover:text-gray-500\")\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex-1 text-left\",\n                                children: item.name\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 15\n                            }, this),\n                            isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: item.href,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"group flex w-full items-center rounded-md px-2 py-2 text-sm font-medium transition-colors\", level > 0 ? \"pl-8\" : \"\", isCurrent ? \"bg-blue-100 text-blue-900\" : \"text-gray-600 hover:bg-gray-50 hover:text-gray-900\"),\n                        onClick: ()=>setIsMobileMenuOpen(false),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"mr-3 h-5 w-5 flex-shrink-0\", isCurrent ? \"text-blue-500\" : \"text-gray-400 group-hover:text-gray-500\")\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, this),\n                            item.name\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this),\n                hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 space-y-1\",\n                    children: item.children.map((child)=>renderNavigationItem(child, level + 1))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, item.name, true, {\n            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n            lineNumber: 106,\n            columnNumber: 7\n        }, this);\n    };\n    const sidebarContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-full flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-16 flex-shrink-0 items-center px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 w-8 rounded-lg bg-blue-600 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"h-5 w-5 text-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 text-xl font-bold text-gray-900\",\n                            children: \"VeriAPI\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 space-y-1 px-2 py-4\",\n                children: navigation.map((item)=>renderNavigationItem(item))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                lineNumber: 177,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 border-t border-gray-200 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium text-white\",\n                                children: user?.firstName?.charAt(0) || \"U\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3 flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-gray-900\",\n                                    children: user?.fullName || \"Usuario\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: user?.email\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: logout,\n                            className: \"ml-3 flex-shrink-0 rounded-md p-1 text-gray-400 hover:text-gray-500\",\n                            title: \"Cerrar Sesi\\xf3n\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    type: \"button\",\n                    className: \"inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500\",\n                    onClick: ()=>setIsMobileMenuOpen(true),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        className: \"h-6 w-6\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            isMobileMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 lg:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 bg-gray-600 bg-opacity-75\",\n                        onClick: ()=>setIsMobileMenuOpen(false)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-y-0 left-0 flex w-full max-w-xs flex-col bg-white\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 right-0 -mr-12 pt-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"ml-1 flex h-10 w-10 items-center justify-center rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white\",\n                                    onClick: ()=>setIsMobileMenuOpen(false),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_ChevronDown_ChevronRight_CreditCard_FileText_LayoutDashboard_LogOut_Menu_Plug_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-6 w-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 13\n                            }, this),\n                            sidebarContent\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                lineNumber: 222,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0\", className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col flex-grow bg-white border-r border-gray-200 overflow-y-auto\",\n                    children: sidebarContent\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/layout/Sidebar.tsx\",\n                lineNumber: 240,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/Badge.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Badge = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, variant = \"default\", children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"badge\", {\n            \"badge-default\": variant === \"default\",\n            \"badge-secondary\": variant === \"secondary\",\n            \"badge-destructive\": variant === \"destructive\",\n            \"badge-outline\": variant === \"outline\"\n        }, className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/ui/Badge.tsx\",\n        lineNumber: 12,\n        columnNumber: 7\n    }, undefined);\n});\nBadge.displayName = \"Badge\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Badge);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Badge.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, variant = \"primary\", size = \"md\", loading = false, disabled, children, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"btn\", {\n            \"btn-primary\": variant === \"primary\",\n            \"btn-secondary\": variant === \"secondary\",\n            \"btn-destructive\": variant === \"destructive\",\n            \"btn-outline\": variant === \"outline\",\n            \"btn-ghost\": variant === \"ghost\",\n            \"btn-sm\": size === \"sm\",\n            \"btn-md\": size === \"md\",\n            \"btn-lg\": size === \"lg\"\n        }, className),\n        disabled: disabled || loading,\n        ref: ref,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"spinner mr-2\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/ui/Button.tsx\",\n                lineNumber: 33,\n                columnNumber: 21\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/ui/Button.tsx\",\n        lineNumber: 14,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Button);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"card\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/ui/Card.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"card-header\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/ui/Card.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"card-title\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/ui/Card.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"card-description\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/ui/Card.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"card-content\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/ui/Card.tsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"card-footer\", className),\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/components/ui/Card.tsx\",\n        lineNumber: 95,\n        columnNumber: 5\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9DYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBMEI7QUFDTztBQTBCakMsTUFBTUUscUJBQU9GLHVEQUFnQixDQUMzQixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsUUFBUSxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQ2xDLDhEQUFDQztRQUNDRCxLQUFLQTtRQUNMSCxXQUFXSCw4Q0FBRUEsQ0FBQyxRQUFRRztRQUNyQixHQUFHRSxLQUFLO2tCQUVSRDs7Ozs7O0FBSVBILEtBQUtPLFdBQVcsR0FBRztBQUVuQixNQUFNQywyQkFBYVYsdURBQWdCLENBQ2pDLENBQUMsRUFBRUksU0FBUyxFQUFFQyxRQUFRLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDbEMsOERBQUNDO1FBQ0NELEtBQUtBO1FBQ0xILFdBQVdILDhDQUFFQSxDQUFDLGVBQWVHO1FBQzVCLEdBQUdFLEtBQUs7a0JBRVJEOzs7Ozs7QUFJUEssV0FBV0QsV0FBVyxHQUFHO0FBRXpCLE1BQU1FLDBCQUFZWCx1REFBZ0IsQ0FDaEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLFFBQVEsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUNsQyw4REFBQ0s7UUFDQ0wsS0FBS0E7UUFDTEgsV0FBV0gsOENBQUVBLENBQUMsY0FBY0c7UUFDM0IsR0FBR0UsS0FBSztrQkFFUkQ7Ozs7OztBQUlQTSxVQUFVRixXQUFXLEdBQUc7QUFFeEIsTUFBTUksZ0NBQWtCYix1REFBZ0IsQ0FDdEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLFFBQVEsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUNsQyw4REFBQ087UUFDQ1AsS0FBS0E7UUFDTEgsV0FBV0gsOENBQUVBLENBQUMsb0JBQW9CRztRQUNqQyxHQUFHRSxLQUFLO2tCQUVSRDs7Ozs7O0FBSVBRLGdCQUFnQkosV0FBVyxHQUFHO0FBRTlCLE1BQU1NLDRCQUFjZix1REFBZ0IsQ0FDbEMsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLFFBQVEsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUNsQyw4REFBQ0M7UUFDQ0QsS0FBS0E7UUFDTEgsV0FBV0gsOENBQUVBLENBQUMsZ0JBQWdCRztRQUM3QixHQUFHRSxLQUFLO2tCQUVSRDs7Ozs7O0FBSVBVLFlBQVlOLFdBQVcsR0FBRztBQUUxQixNQUFNTywyQkFBYWhCLHVEQUFnQixDQUNqQyxDQUFDLEVBQUVJLFNBQVMsRUFBRUMsUUFBUSxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQ2xDLDhEQUFDQztRQUNDRCxLQUFLQTtRQUNMSCxXQUFXSCw4Q0FBRUEsQ0FBQyxlQUFlRztRQUM1QixHQUFHRSxLQUFLO2tCQUVSRDs7Ozs7O0FBSVBXLFdBQVdQLFdBQVcsR0FBRztBQUV3RCIsInNvdXJjZXMiOlsid2VicGFjazovL3ZlcmlhcGktZGFzaGJvYXJkLy4vc3JjL2NvbXBvbmVudHMvdWkvQ2FyZC50c3g/OGRkMiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XG5cbmludGVyZmFjZSBDYXJkUHJvcHMgZXh0ZW5kcyBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD4ge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufVxuXG5pbnRlcmZhY2UgQ2FyZEhlYWRlclByb3BzIGV4dGVuZHMgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn1cblxuaW50ZXJmYWNlIENhcmRUaXRsZVByb3BzIGV4dGVuZHMgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTEhlYWRpbmdFbGVtZW50PiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59XG5cbmludGVyZmFjZSBDYXJkRGVzY3JpcHRpb25Qcm9wcyBleHRlbmRzIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxQYXJhZ3JhcGhFbGVtZW50PiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59XG5cbmludGVyZmFjZSBDYXJkQ29udGVudFByb3BzIGV4dGVuZHMgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn1cblxuaW50ZXJmYWNlIENhcmRGb290ZXJQcm9wcyBleHRlbmRzIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50PiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59XG5cbmNvbnN0IENhcmQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxEaXZFbGVtZW50LCBDYXJkUHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIGNoaWxkcmVuLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgICA8ZGl2XG4gICAgICByZWY9e3JlZn1cbiAgICAgIGNsYXNzTmFtZT17Y24oJ2NhcmQnLCBjbGFzc05hbWUpfVxuICAgICAgey4uLnByb3BzfVxuICAgID5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L2Rpdj5cbiAgKVxuKTtcbkNhcmQuZGlzcGxheU5hbWUgPSAnQ2FyZCc7XG5cbmNvbnN0IENhcmRIZWFkZXIgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxEaXZFbGVtZW50LCBDYXJkSGVhZGVyUHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIGNoaWxkcmVuLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgICA8ZGl2XG4gICAgICByZWY9e3JlZn1cbiAgICAgIGNsYXNzTmFtZT17Y24oJ2NhcmQtaGVhZGVyJywgY2xhc3NOYW1lKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICA+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9kaXY+XG4gIClcbik7XG5DYXJkSGVhZGVyLmRpc3BsYXlOYW1lID0gJ0NhcmRIZWFkZXInO1xuXG5jb25zdCBDYXJkVGl0bGUgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxQYXJhZ3JhcGhFbGVtZW50LCBDYXJkVGl0bGVQcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgY2hpbGRyZW4sIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICAgIDxoM1xuICAgICAgcmVmPXtyZWZ9XG4gICAgICBjbGFzc05hbWU9e2NuKCdjYXJkLXRpdGxlJywgY2xhc3NOYW1lKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICA+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9oMz5cbiAgKVxuKTtcbkNhcmRUaXRsZS5kaXNwbGF5TmFtZSA9ICdDYXJkVGl0bGUnO1xuXG5jb25zdCBDYXJkRGVzY3JpcHRpb24gPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxQYXJhZ3JhcGhFbGVtZW50LCBDYXJkRGVzY3JpcHRpb25Qcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgY2hpbGRyZW4sIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICAgIDxwXG4gICAgICByZWY9e3JlZn1cbiAgICAgIGNsYXNzTmFtZT17Y24oJ2NhcmQtZGVzY3JpcHRpb24nLCBjbGFzc05hbWUpfVxuICAgICAgey4uLnByb3BzfVxuICAgID5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L3A+XG4gIClcbik7XG5DYXJkRGVzY3JpcHRpb24uZGlzcGxheU5hbWUgPSAnQ2FyZERlc2NyaXB0aW9uJztcblxuY29uc3QgQ2FyZENvbnRlbnQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxEaXZFbGVtZW50LCBDYXJkQ29udGVudFByb3BzPihcbiAgKHsgY2xhc3NOYW1lLCBjaGlsZHJlbiwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gICAgPGRpdlxuICAgICAgcmVmPXtyZWZ9XG4gICAgICBjbGFzc05hbWU9e2NuKCdjYXJkLWNvbnRlbnQnLCBjbGFzc05hbWUpfVxuICAgICAgey4uLnByb3BzfVxuICAgID5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L2Rpdj5cbiAgKVxuKTtcbkNhcmRDb250ZW50LmRpc3BsYXlOYW1lID0gJ0NhcmRDb250ZW50JztcblxuY29uc3QgQ2FyZEZvb3RlciA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTERpdkVsZW1lbnQsIENhcmRGb290ZXJQcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgY2hpbGRyZW4sIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICAgIDxkaXZcbiAgICAgIHJlZj17cmVmfVxuICAgICAgY2xhc3NOYW1lPXtjbignY2FyZC1mb290ZXInLCBjbGFzc05hbWUpfVxuICAgICAgey4uLnByb3BzfVxuICAgID5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L2Rpdj5cbiAgKVxuKTtcbkNhcmRGb290ZXIuZGlzcGxheU5hbWUgPSAnQ2FyZEZvb3Rlcic7XG5cbmV4cG9ydCB7IENhcmQsIENhcmRIZWFkZXIsIENhcmRUaXRsZSwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkQ29udGVudCwgQ2FyZEZvb3RlciB9O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiY24iLCJDYXJkIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsImNoaWxkcmVuIiwicHJvcHMiLCJyZWYiLCJkaXYiLCJkaXNwbGF5TmFtZSIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJoMyIsIkNhcmREZXNjcmlwdGlvbiIsInAiLCJDYXJkQ29udGVudCIsIkNhcmRGb290ZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useRequireAuth: () => (/* binding */ useRequireAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,useRequireAuth auto */ \n\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Check if user is authenticated on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkAuth = async ()=>{\n            try {\n                if (_lib_api__WEBPACK_IMPORTED_MODULE_4__.authApi.isAuthenticated()) {\n                    // If we have a token, we could fetch user data here\n                    // For now, we'll just set loading to false\n                    // In a real app, you might want to validate the token with the server\n                    setLoading(false);\n                } else {\n                    setLoading(false);\n                }\n            } catch (error) {\n                console.error(\"Auth check failed:\", error);\n                setLoading(false);\n            }\n        };\n        checkAuth();\n    }, []);\n    const login = async (credentials)=>{\n        try {\n            setLoading(true);\n            console.log(\"Attempting login with:\", credentials);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.authApi.login(credentials);\n            console.log(\"Login response:\", response);\n            setUser(response.user);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"\\xa1Bienvenido de vuelta!\");\n            router.push(\"/dashboard\");\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            const message = error.response?.data?.message || error.response?.data?.error || error.message || \"Error al iniciar sesi\\xf3n\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error(message);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const register = async (data)=>{\n        try {\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.authApi.register(data);\n            setUser(response.user);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"\\xa1Cuenta creada exitosamente!\");\n            router.push(\"/dashboard\");\n        } catch (error) {\n            const message = error.response?.data?.error || \"Error al crear la cuenta\";\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error(message);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = ()=>{\n        setUser(null);\n        _lib_api__WEBPACK_IMPORTED_MODULE_4__.authApi.logout();\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Sesi\\xf3n cerrada correctamente\");\n    };\n    const value = {\n        user,\n        loading,\n        login,\n        register,\n        logout,\n        isAuthenticated: !!user || _lib_api__WEBPACK_IMPORTED_MODULE_4__.authApi.isAuthenticated()\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/hooks/useAuth.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// Hook for protecting routes\nfunction useRequireAuth() {\n    const { isAuthenticated, loading } = useAuth();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !isAuthenticated) {\n            router.push(\"/login\");\n        }\n    }, [\n        isAuthenticated,\n        loading,\n        router\n    ]);\n    return {\n        isAuthenticated,\n        loading\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuth.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: () => (/* binding */ authApi),\n/* harmony export */   companyApi: () => (/* binding */ companyApi),\n/* harmony export */   customersApi: () => (/* binding */ customersApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   invoicesApi: () => (/* binding */ invoicesApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n// API Configuration\nconst API_BASE_URL = \"http://localhost:3000/api\" || 0;\nconst TOKEN_KEY = \"veriapi_token\";\n// Create axios instance\nconst createApiClient = ()=>{\n    const client = axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n        baseURL: API_BASE_URL,\n        timeout: 30000,\n        headers: {\n            \"Content-Type\": \"application/json\"\n        }\n    });\n    // Request interceptor to add auth token\n    client.interceptors.request.use((config)=>{\n        console.log(\"API Request:\", config.method?.toUpperCase(), config.url, config.data);\n        const token = js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(TOKEN_KEY);\n        if (token) {\n            config.headers.Authorization = `Bearer ${token}`;\n        }\n        return config;\n    }, (error)=>{\n        console.error(\"Request interceptor error:\", error);\n        return Promise.reject(error);\n    });\n    // Response interceptor for error handling\n    client.interceptors.response.use((response)=>{\n        console.log(\"API Response:\", response.status, response.config.url, response.data);\n        return response;\n    }, (error)=>{\n        console.error(\"API Error:\", error.response?.status, error.response?.data, error.message);\n        const message = error.response?.data?.message || error.response?.data?.error || error.message || \"An error occurred\";\n        if (error.response?.status === 401) {\n            // Unauthorized - clear token and redirect to login\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(TOKEN_KEY);\n            if (false) {}\n        } else if (error.response?.status >= 500) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Server error. Please try again later.\");\n        } else {\n            // Don't show toast here, let the calling component handle it\n            console.log(\"API error message:\", message);\n        }\n        return Promise.reject(error);\n    });\n    return client;\n};\nconst api = createApiClient();\n// Auth API\nconst authApi = {\n    login: async (credentials)=>{\n        const response = await api.post(\"/auth/login\", credentials);\n        // Store token in cookie\n        if (response.data.token) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(TOKEN_KEY, response.data.token, {\n                expires: 7,\n                secure: \"development\" === \"production\",\n                sameSite: \"strict\"\n            });\n        }\n        return response.data;\n    },\n    register: async (data)=>{\n        const response = await api.post(\"/auth/register\", data);\n        // Store token in cookie\n        if (response.data.token) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(TOKEN_KEY, response.data.token, {\n                expires: 7,\n                secure: \"development\" === \"production\",\n                sameSite: \"strict\"\n            });\n        }\n        return response.data;\n    },\n    logout: ()=>{\n        js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(TOKEN_KEY);\n        if (false) {}\n    },\n    getToken: ()=>{\n        return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(TOKEN_KEY);\n    },\n    isAuthenticated: ()=>{\n        return !!js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(TOKEN_KEY);\n    }\n};\n// Company API\nconst companyApi = {\n    getCompany: async ()=>{\n        const response = await api.get(\"/company\");\n        return response.data.company;\n    },\n    updateCompany: async (data)=>{\n        const response = await api.put(\"/company\", data);\n        return response.data.company;\n    },\n    getStats: async ()=>{\n        const response = await api.get(\"/company/stats\");\n        return response.data.stats;\n    },\n    getNextInvoiceNumber: async ()=>{\n        const response = await api.get(\"/company/next-invoice-number\");\n        return response.data;\n    }\n};\n// Customers API\nconst customersApi = {\n    getCustomers: async (filters)=>{\n        const params = new URLSearchParams();\n        if (filters?.page) params.append(\"page\", filters.page.toString());\n        if (filters?.limit) params.append(\"limit\", filters.limit.toString());\n        if (filters?.search) params.append(\"search\", filters.search);\n        if (filters?.customerType) params.append(\"customerType\", filters.customerType);\n        if (filters?.isActive !== undefined) params.append(\"isActive\", filters.isActive.toString());\n        const response = await api.get(`/customers?${params.toString()}`);\n        return response.data;\n    },\n    getCustomer: async (id)=>{\n        const response = await api.get(`/customers/${id}`);\n        return response.data.customer;\n    },\n    createCustomer: async (data)=>{\n        const response = await api.post(\"/customers\", data);\n        return response.data.customer;\n    },\n    updateCustomer: async (id, data)=>{\n        const response = await api.put(`/customers/${id}`, data);\n        return response.data.customer;\n    },\n    deleteCustomer: async (id)=>{\n        await api.delete(`/customers/${id}`);\n    }\n};\n// Invoices API\nconst invoicesApi = {\n    getInvoices: async (filters)=>{\n        const params = new URLSearchParams();\n        if (filters?.page) params.append(\"page\", filters.page.toString());\n        if (filters?.limit) params.append(\"limit\", filters.limit.toString());\n        if (filters?.status) params.append(\"status\", filters.status);\n        if (filters?.customer) params.append(\"customer\", filters.customer);\n        if (filters?.dateFrom) params.append(\"dateFrom\", filters.dateFrom);\n        if (filters?.dateTo) params.append(\"dateTo\", filters.dateTo);\n        if (filters?.platform) params.append(\"platform\", filters.platform);\n        if (filters?.search) params.append(\"search\", filters.search);\n        const response = await api.get(`/invoices?${params.toString()}`);\n        return response.data;\n    },\n    getInvoice: async (id)=>{\n        const response = await api.get(`/invoices/${id}`);\n        return response.data.invoice;\n    },\n    createInvoice: async (data)=>{\n        const response = await api.post(\"/invoices\", data);\n        return response.data.invoice;\n    },\n    updateInvoice: async (id, data)=>{\n        const response = await api.put(`/invoices/${id}`, data);\n        return response.data.invoice;\n    },\n    deleteInvoice: async (id)=>{\n        await api.delete(`/invoices/${id}`);\n    },\n    downloadPdf: async (id)=>{\n        const response = await api.get(`/invoices/${id}/pdf`, {\n            responseType: \"blob\"\n        });\n        return response.data;\n    },\n    recordPayment: async (id, payment)=>{\n        const response = await api.post(`/invoices/${id}/payment`, payment);\n        return response.data.invoice;\n    }\n};\n// Export the main API client for custom requests\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   capitalizeFirst: () => (/* binding */ capitalizeFirst),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   downloadBlob: () => (/* binding */ downloadBlob),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   formatRelativeDate: () => (/* binding */ formatRelativeDate),\n/* harmony export */   getFromStorage: () => (/* binding */ getFromStorage),\n/* harmony export */   getInvoiceStatusColor: () => (/* binding */ getInvoiceStatusColor),\n/* harmony export */   getPaymentStatusColor: () => (/* binding */ getPaymentStatusColor),\n/* harmony export */   groupBy: () => (/* binding */ groupBy),\n/* harmony export */   removeFromStorage: () => (/* binding */ removeFromStorage),\n/* harmony export */   setToStorage: () => (/* binding */ setToStorage),\n/* harmony export */   slugify: () => (/* binding */ slugify),\n/* harmony export */   sortBy: () => (/* binding */ sortBy),\n/* harmony export */   truncateText: () => (/* binding */ truncateText),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validateIBAN: () => (/* binding */ validateIBAN),\n/* harmony export */   validateSpanishCIF: () => (/* binding */ validateSpanishCIF),\n/* harmony export */   validateSpanishNIE: () => (/* binding */ validateSpanishNIE),\n/* harmony export */   validateSpanishNIF: () => (/* binding */ validateSpanishNIF),\n/* harmony export */   validateSpanishPhone: () => (/* binding */ validateSpanishPhone),\n/* harmony export */   validateSpanishPostalCode: () => (/* binding */ validateSpanishPostalCode)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=format,isValid,parseISO!=!date-fns */ \"(ssr)/./node_modules/date-fns/parseISO.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=format,isValid,parseISO!=!date-fns */ \"(ssr)/./node_modules/date-fns/isValid.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format,isValid,parseISO!=!date-fns */ \"(ssr)/./node_modules/date-fns/format.mjs\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! date-fns/locale */ \"(ssr)/./node_modules/date-fns/locale/es.mjs\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! date-fns/locale */ \"(ssr)/./node_modules/date-fns/locale/en-US.mjs\");\n\n\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// Date formatting utilities\nconst formatDate = (date, locale = \"es\")=>{\n    try {\n        const dateObj = typeof date === \"string\" ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_3__.isValid)(dateObj)) return \"\";\n        const localeObj = locale === \"es\" ? date_fns_locale__WEBPACK_IMPORTED_MODULE_4__.es : date_fns_locale__WEBPACK_IMPORTED_MODULE_5__.enUS;\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_6__.format)(dateObj, \"dd/MM/yyyy\", {\n            locale: localeObj\n        });\n    } catch  {\n        return \"\";\n    }\n};\nconst formatDateTime = (date, locale = \"es\")=>{\n    try {\n        const dateObj = typeof date === \"string\" ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_3__.isValid)(dateObj)) return \"\";\n        const localeObj = locale === \"es\" ? date_fns_locale__WEBPACK_IMPORTED_MODULE_4__.es : date_fns_locale__WEBPACK_IMPORTED_MODULE_5__.enUS;\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_6__.format)(dateObj, \"dd/MM/yyyy HH:mm\", {\n            locale: localeObj\n        });\n    } catch  {\n        return \"\";\n    }\n};\nconst formatRelativeDate = (date, locale = \"es\")=>{\n    try {\n        const dateObj = typeof date === \"string\" ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.parseISO)(date) : date;\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_3__.isValid)(dateObj)) return \"\";\n        const now = new Date();\n        const diffInDays = Math.floor((now.getTime() - dateObj.getTime()) / (1000 * 60 * 60 * 24));\n        if (diffInDays === 0) return locale === \"es\" ? \"Hoy\" : \"Today\";\n        if (diffInDays === 1) return locale === \"es\" ? \"Ayer\" : \"Yesterday\";\n        if (diffInDays < 7) return locale === \"es\" ? `Hace ${diffInDays} días` : `${diffInDays} days ago`;\n        return formatDate(dateObj, locale);\n    } catch  {\n        return \"\";\n    }\n};\n// Currency formatting\nconst formatCurrency = (amount, currency = \"EUR\", locale = \"es\")=>{\n    try {\n        const localeCode = locale === \"es\" ? \"es-ES\" : \"en-US\";\n        return new Intl.NumberFormat(localeCode, {\n            style: \"currency\",\n            currency,\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 2\n        }).format(amount);\n    } catch  {\n        return `${amount.toFixed(2)} ${currency}`;\n    }\n};\nconst formatNumber = (number, locale = \"es\")=>{\n    try {\n        const localeCode = locale === \"es\" ? \"es-ES\" : \"en-US\";\n        return new Intl.NumberFormat(localeCode).format(number);\n    } catch  {\n        return number.toString();\n    }\n};\nconst formatPercentage = (value, locale = \"es\")=>{\n    try {\n        const localeCode = locale === \"es\" ? \"es-ES\" : \"en-US\";\n        return new Intl.NumberFormat(localeCode, {\n            style: \"percent\",\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 2\n        }).format(value / 100);\n    } catch  {\n        return `${value}%`;\n    }\n};\n// Status utilities\nconst getInvoiceStatusColor = (status)=>{\n    switch(status){\n        case \"draft\":\n            return \"bg-gray-100 text-gray-800\";\n        case \"sent\":\n            return \"bg-blue-100 text-blue-800\";\n        case \"viewed\":\n            return \"bg-yellow-100 text-yellow-800\";\n        case \"paid\":\n            return \"bg-green-100 text-green-800\";\n        case \"overdue\":\n            return \"bg-red-100 text-red-800\";\n        case \"cancelled\":\n            return \"bg-gray-100 text-gray-800\";\n        default:\n            return \"bg-gray-100 text-gray-800\";\n    }\n};\nconst getPaymentStatusColor = (status)=>{\n    switch(status){\n        case \"pending\":\n            return \"bg-yellow-100 text-yellow-800\";\n        case \"partial\":\n            return \"bg-orange-100 text-orange-800\";\n        case \"paid\":\n            return \"bg-green-100 text-green-800\";\n        case \"overdue\":\n            return \"bg-red-100 text-red-800\";\n        default:\n            return \"bg-gray-100 text-gray-800\";\n    }\n};\n// Validation utilities\nconst validateEmail = (email)=>{\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n};\nconst validateSpanishNIF = (nif)=>{\n    const nifRegex = /^[0-9]{8}[TRWAGMYFPDXBNJZSQVHLCKE]$/i;\n    return nifRegex.test(nif);\n};\nconst validateSpanishCIF = (cif)=>{\n    const cifRegex = /^[ABCDEFGHJNPQRSUVW][0-9]{7}[0-9A-J]$/i;\n    return cifRegex.test(cif);\n};\nconst validateSpanishNIE = (nie)=>{\n    const nieRegex = /^[XYZ][0-9]{7}[TRWAGMYFPDXBNJZSQVHLCKE]$/i;\n    return nieRegex.test(nie);\n};\nconst validateSpanishPostalCode = (postalCode)=>{\n    const postalCodeRegex = /^[0-9]{5}$/;\n    return postalCodeRegex.test(postalCode);\n};\nconst validateSpanishPhone = (phone)=>{\n    const phoneRegex = /^(\\+34|0034|34)?[6789][0-9]{8}$/;\n    return phoneRegex.test(phone.replace(/\\s/g, \"\"));\n};\nconst validateIBAN = (iban)=>{\n    const ibanRegex = /^[A-Z]{2}[0-9]{2}[A-Z0-9]{4}[0-9]{7}([A-Z0-9]?){0,16}$/;\n    return ibanRegex.test(iban.replace(/\\s/g, \"\"));\n};\n// File utilities\nconst downloadBlob = (blob, filename)=>{\n    const url = window.URL.createObjectURL(blob);\n    const link = document.createElement(\"a\");\n    link.href = url;\n    link.download = filename;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    window.URL.revokeObjectURL(url);\n};\n// Text utilities\nconst truncateText = (text, maxLength)=>{\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + \"...\";\n};\nconst capitalizeFirst = (text)=>{\n    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();\n};\nconst slugify = (text)=>{\n    return text.toLowerCase().replace(/[^\\w\\s-]/g, \"\").replace(/[\\s_-]+/g, \"-\").replace(/^-+|-+$/g, \"\");\n};\n// Array utilities\nconst groupBy = (array, key)=>{\n    return array.reduce((groups, item)=>{\n        const group = key(item);\n        groups[group] = groups[group] || [];\n        groups[group].push(item);\n        return groups;\n    }, {});\n};\nconst sortBy = (array, key, direction = \"asc\")=>{\n    return [\n        ...array\n    ].sort((a, b)=>{\n        const aVal = a[key];\n        const bVal = b[key];\n        if (aVal < bVal) return direction === \"asc\" ? -1 : 1;\n        if (aVal > bVal) return direction === \"asc\" ? 1 : -1;\n        return 0;\n    });\n};\n// Local storage utilities\nconst getFromStorage = (key, defaultValue)=>{\n    if (true) return defaultValue;\n    try {\n        const item = window.localStorage.getItem(key);\n        return item ? JSON.parse(item) : defaultValue;\n    } catch  {\n        return defaultValue;\n    }\n};\nconst setToStorage = (key, value)=>{\n    if (true) return;\n    try {\n        window.localStorage.setItem(key, JSON.stringify(value));\n    } catch  {\n    // Handle storage errors silently\n    }\n};\nconst removeFromStorage = (key)=>{\n    if (true) return;\n    try {\n        window.localStorage.removeItem(key);\n    } catch  {\n    // Handle storage errors silently\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"05dce48e3234\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdmVyaWFwaS1kYXNoYm9hcmQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzlhMGMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwNWRjZTQ4ZTMyMzRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboard/invoices/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/dashboard/invoices/page.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(rsc)/./src/hooks/useAuth.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: \"VeriAPI Dashboard - Electronic Invoicing Management\",\n    description: \"Manage your electronic invoices, customers, and integrations with VeriAPI\",\n    keywords: [\n        \"invoicing\",\n        \"electronic invoicing\",\n        \"SaaS\",\n        \"Spanish invoicing\",\n        \"facturaci\\xf3n electr\\xf3nica\"\n    ],\n    authors: [\n        {\n            name: \"VeriAPI Team\"\n        }\n    ],\n    robots: \"index, follow\",\n    openGraph: {\n        title: \"VeriAPI Dashboard\",\n        description: \"Electronic Invoicing Management System\",\n        type: \"website\",\n        locale: \"es_ES\",\n        alternateLocale: \"en_US\"\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"es\",\n        className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\"\n                            },\n                            success: {\n                                duration: 3000,\n                                iconTheme: {\n                                    primary: \"#10b981\",\n                                    secondary: \"#fff\"\n                                }\n                            },\n                            error: {\n                                duration: 5000,\n                                iconTheme: {\n                                    primary: \"#ef4444\",\n                                    secondary: \"#fff\"\n                                }\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/layout.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/layout.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/layout.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   useRequireAuth: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/hooks/useAuth.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/hooks/useAuth.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/hooks/useAuth.tsx#useRequireAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/date-fns","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/react-hot-toast","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/js-cookie","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/clsx","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Finvoices%2Fpage&page=%2Fdashboard%2Finvoices%2Fpage&appPaths=%2Fdashboard%2Finvoices%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Finvoices%2Fpage.tsx&appDir=%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmacnabil%2FDocuments%2FNABIL%2FDESKTOP%2FSAAS%20PROJECTS%2FVERIAPI%2Fdashboard&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();