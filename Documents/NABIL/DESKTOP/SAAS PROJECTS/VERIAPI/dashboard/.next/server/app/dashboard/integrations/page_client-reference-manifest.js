globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/dashboard/integrations/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/page.tsx":{"*":{"id":"(ssr)/./src/app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/hooks/useAuth.tsx":{"*":{"id":"(ssr)/./src/hooks/useAuth.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(ssr)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/invoices/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/invoices/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/customers/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/customers/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/integrations/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/integrations/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/subscription/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/subscription/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/settings/company/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/settings/company/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/settings/profile/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/settings/profile/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/page.tsx":{"id":"(app-pages-browser)/./src/app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/node_modules/react-hot-toast/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/hooks/useAuth.tsx":{"id":"(app-pages-browser)/./src/hooks/useAuth.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/login/page.tsx":{"id":"(app-pages-browser)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/page.tsx","name":"*","chunks":["app/dashboard/page","static/chunks/app/dashboard/page.js"],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/invoices/page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/invoices/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/customers/page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/customers/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/integrations/page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/integrations/page.tsx","name":"*","chunks":["app/dashboard/integrations/page","static/chunks/app/dashboard/integrations/page.js"],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/subscription/page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/subscription/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/settings/company/page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/settings/company/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/settings/profile/page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/settings/profile/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/":[],"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/page":[],"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/layout":["static/css/app/layout.css"],"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/page":[],"/Users/<USER>/Documents/NABIL/DESKTOP/SAAS PROJECTS/VERIAPI/dashboard/src/app/dashboard/integrations/page":[]}}