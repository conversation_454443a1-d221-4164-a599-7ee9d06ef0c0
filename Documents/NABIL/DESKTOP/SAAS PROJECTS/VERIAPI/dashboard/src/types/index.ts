// Core API Types
export interface ApiResponse<T = any> {
  message?: string;
  data?: T;
  error?: string;
  details?: any[];
}

// User & Authentication Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  role: 'admin' | 'user';
  lastLogin?: string;
  company: Company;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  message: string;
  token: string;
  user: User;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  companyName: string;
  companyEmail: string;
  companyAddress: string;
  companyCity: string;
  companyPostalCode: string;
  companyPhone: string;
  companyCif: string;
  companyNif?: string;
}

// Company Types
export interface Company {
  id: string;
  name: string;
  tradeName?: string;
  nif?: string;
  cif?: string;
  nie?: string;
  vatNumber?: string;
  address: {
    street: string;
    number?: string;
    floor?: string;
    door?: string;
    city: string;
    province?: string;
    postalCode: string;
    country: string;
  };
  contact: {
    phone?: string;
    mobile?: string;
    email: string;
    website?: string;
  };
  businessType: string;
  sector?: string;
  taxSettings: {
    defaultTaxRate: number;
    taxRates: TaxRate[];
    isVatExempt: boolean;
    vatExemptReason?: string;
    irpfRate: number;
  };
  invoiceSettings: {
    prefix: string;
    nextNumber: number;
    numberLength: number;
    dueDays: number;
  };
  bankInfo?: {
    bankName?: string;
    iban?: string;
    swift?: string;
  };
  logo?: string;
  isActive: boolean;
  subscription: {
    plan: 'free' | 'basic' | 'premium' | 'enterprise';
    status: 'active' | 'inactive' | 'suspended' | 'cancelled';
    expiresAt?: string;
    maxInvoicesPerMonth: number;
  };
  createdAt: string;
  updatedAt: string;
}

export interface TaxRate {
  name: string;
  rate: number;
  description?: string;
}

// Customer Types
export interface Customer {
  id: string;
  name: string;
  tradeName?: string;
  customerType: 'individual' | 'company';
  nif?: string;
  cif?: string;
  nie?: string;
  vatNumber?: string;
  address: {
    street: string;
    number?: string;
    floor?: string;
    door?: string;
    city: string;
    province?: string;
    postalCode: string;
    country: string;
  };
  contact: {
    phone?: string;
    mobile?: string;
    email?: string;
    website?: string;
  };
  taxInfo: {
    defaultTaxRate: number;
    isVatExempt: boolean;
    vatExemptReason?: string;
  };
  paymentInfo: {
    paymentTerms: number;
    preferredPaymentMethod: 'transfer' | 'card' | 'cash' | 'check' | 'other';
    bankAccount?: {
      bankName?: string;
      iban?: string;
    };
    creditLimit: number;
  };
  customerSince: string;
  notes?: string;
  tags: string[];
  isActive: boolean;
  company: string;
  stats: {
    totalInvoices: number;
    totalAmount: number;
    lastInvoiceDate?: string;
    averagePaymentDays: number;
  };
  createdAt: string;
  updatedAt: string;
}

// Invoice Types
export interface InvoiceLine {
  description: string;
  quantity: number;
  unitPrice: number;
  discount?: number;
  taxRate: number;
  taxName?: string;
  productCode?: string;
  unit?: string;
  subtotal: number;
  discountAmount: number;
  netAmount: number;
  taxAmount: number;
  total: number;
}

export interface Invoice {
  id: string;
  invoiceNumber: string;
  series: string;
  issueDate: string;
  dueDate: string;
  serviceDate: string;
  customer: Customer;
  company: Company;
  lines: InvoiceLine[];
  subtotal: number;
  totalDiscount: number;
  netAmount: number;
  totalTax: number;
  total: number;
  currency: string;
  exchangeRate: number;
  status: 'draft' | 'sent' | 'viewed' | 'paid' | 'overdue' | 'cancelled';
  paymentInfo: {
    status: 'pending' | 'partial' | 'paid' | 'overdue';
    paidAmount: number;
    payments: Payment[];
  };
  compliance: {
    irpfRate: number;
    irpfAmount?: number;
    reverseCharge: boolean;
    specialRegime: string;
  };
  notes?: string;
  externalReferences?: {
    woocommerceOrderId?: string;
    shopifyOrderId?: string;
    prestashopOrderId?: string;
    platform?: string;
    externalId?: string;
  };
  sentDate?: string;
  viewedDate?: string;
  pdfGenerated: boolean;
  pdfPath?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Payment {
  amount: number;
  paymentDate: string;
  method: 'transfer' | 'card' | 'cash' | 'check' | 'other';
  reference?: string;
  notes?: string;
  createdAt: string;
}

// Dashboard Stats Types
export interface DashboardStats {
  invoices: {
    totalInvoices: number;
    totalAmount: number;
    paidAmount: number;
    pendingAmount: number;
    thisMonthInvoices: number;
    thisMonthAmount: number;
  };
  customers: {
    totalCustomers: number;
    activeCustomers: number;
  };
  company: {
    subscription: Company['subscription'];
    maxInvoicesPerMonth: number;
    remainingInvoices: number;
  };
}

// API Query Types
export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface InvoiceFilters extends PaginationParams {
  status?: Invoice['status'];
  customer?: string;
  dateFrom?: string;
  dateTo?: string;
  platform?: string;
  search?: string;
}

export interface CustomerFilters extends PaginationParams {
  search?: string;
  customerType?: Customer['customerType'];
  isActive?: boolean;
}

// Form Types
export interface CreateCustomerRequest {
  name: string;
  tradeName?: string;
  customerType: 'individual' | 'company';
  nif?: string;
  cif?: string;
  nie?: string;
  vatNumber?: string;
  address: {
    street: string;
    number?: string;
    floor?: string;
    door?: string;
    city: string;
    province?: string;
    postalCode: string;
    country: string;
  };
  contact: {
    phone?: string;
    mobile?: string;
    email?: string;
    website?: string;
  };
  taxInfo?: {
    defaultTaxRate?: number;
    isVatExempt?: boolean;
    vatExemptReason?: string;
  };
  paymentInfo?: {
    paymentTerms?: number;
    preferredPaymentMethod?: 'transfer' | 'card' | 'cash' | 'check' | 'other';
    creditLimit?: number;
  };
  notes?: string;
  tags?: string[];
}

export interface CreateInvoiceRequest {
  customer: string;
  lines: Omit<InvoiceLine, 'subtotal' | 'discountAmount' | 'netAmount' | 'taxAmount' | 'total'>[];
  dueDate?: string;
  serviceDate?: string;
  notes?: string;
  compliance?: {
    irpfRate?: number;
    reverseCharge?: boolean;
    specialRegime?: string;
  };
  externalReferences?: {
    woocommerceOrderId?: string;
    shopifyOrderId?: string;
    prestashopOrderId?: string;
    platform?: string;
    externalId?: string;
  };
}
