{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "create": "Create", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "download": "Download", "upload": "Upload", "yes": "Yes", "no": "No", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "refresh": "Refresh", "total": "Total", "subtotal": "Subtotal", "tax": "Tax", "discount": "Discount", "amount": "Amount", "date": "Date", "status": "Status", "actions": "Actions", "name": "Name", "email": "Email", "phone": "Phone", "address": "Address", "city": "City", "postalCode": "Postal Code", "country": "Country", "notes": "Notes"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "register": "Register", "email": "Email", "password": "Password", "forgotPassword": "Forgot your password?", "rememberMe": "Remember me", "loginSuccess": "Login successful", "loginError": "Login error", "logoutSuccess": "Logout successful", "invalidCredentials": "Invalid credentials", "welcomeBack": "Welcome back!", "signInToAccount": "Sign in to your account"}, "navigation": {"dashboard": "Dashboard", "invoices": "Invoices", "customers": "Customers", "integrations": "Integrations", "subscription": "Subscription", "settings": "Settings", "profile": "Profile", "company": "Company"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome", "overview": "Overview", "thisMonth": "This Month", "thisYear": "This Year", "totalInvoices": "Total Invoices", "totalRevenue": "Total Revenue", "pendingAmount": "Pending Amount", "paidAmount": "<PERSON><PERSON>", "totalCustomers": "Total Customers", "activeCustomers": "Active Customers", "recentInvoices": "Recent Invoices", "recentCustomers": "Recent Customers", "monthlyRevenue": "Monthly Revenue", "invoicesByStatus": "Invoices by Status", "topCustomers": "Top Customers"}, "invoices": {"title": "Invoices", "createInvoice": "Create Invoice", "invoiceNumber": "Invoice Number", "customer": "Customer", "issueDate": "Issue Date", "dueDate": "Due Date", "serviceDate": "Service Date", "total": "Total", "status": "Status", "actions": "Actions", "viewInvoice": "View Invoice", "downloadPdf": "Download PDF", "sendInvoice": "Send Invoice", "recordPayment": "Record Payment", "markAsPaid": "<PERSON> as <PERSON><PERSON>", "cancelInvoice": "Cancel Invoice", "duplicateInvoice": "Duplicate Invoice", "invoiceDetails": "Invoice Details", "lineItems": "Line Items", "description": "Description", "quantity": "Quantity", "unitPrice": "Unit Price", "taxRate": "Tax Rate", "lineTotal": "Line Total", "addLine": "Add Line", "removeLine": "Remove Line", "paymentInfo": "Payment Information", "paymentStatus": "Payment Status", "paidAmount": "<PERSON><PERSON>", "remainingAmount": "Remaining Amount", "paymentHistory": "Payment History", "paymentDate": "Payment Date", "paymentMethod": "Payment Method", "paymentReference": "Payment Reference", "statuses": {"draft": "Draft", "sent": "<PERSON><PERSON>", "viewed": "Viewed", "paid": "Paid", "overdue": "Overdue", "cancelled": "Cancelled"}, "paymentStatuses": {"pending": "Pending", "partial": "Partial", "paid": "Paid", "overdue": "Overdue"}, "filters": {"all": "All", "dateRange": "Date Range", "platform": "Platform", "customer": "Customer"}}, "customers": {"title": "Customers", "createCustomer": "Create Customer", "customerName": "Customer Name", "customerType": "Customer Type", "taxId": "Tax ID", "contact": "Contact", "address": "Address", "paymentTerms": "Payment Terms", "creditLimit": "Credit Limit", "customerSince": "Customer Since", "totalInvoices": "Total Invoices", "totalAmount": "Total Amount", "lastInvoice": "Last Invoice", "averagePaymentDays": "Average Payment Days", "customerDetails": "Customer Details", "editCustomer": "Edit Customer", "deleteCustomer": "Delete Customer", "customerInvoices": "Customer Invoices", "types": {"individual": "Individual", "company": "Company"}, "fields": {"name": "Name", "tradeName": "Trade Name", "nif": "NIF", "cif": "CIF", "nie": "NIE", "vatNumber": "VAT Number", "street": "Street", "number": "Number", "floor": "Floor", "door": "Door", "city": "City", "province": "Province", "postalCode": "Postal Code", "country": "Country", "phone": "Phone", "mobile": "Mobile", "email": "Email", "website": "Website", "paymentTerms": "Payment Terms", "preferredPaymentMethod": "Preferred Payment Method", "bankName": "Bank Name", "iban": "IBAN", "creditLimit": "Credit Limit", "notes": "Notes", "tags": "Tags"}}, "integrations": {"title": "Integrations", "description": "Connect VeriAPI with your favorite platforms", "connected": "Connected", "notConnected": "Not Connected", "connect": "Connect", "disconnect": "Disconnect", "configure": "Configure", "status": "Status", "lastSync": "Last Sync", "totalOrders": "Total Orders", "platforms": {"woocommerce": "WooCommerce", "shopify": "Shopify", "prestashop": "PrestaShop", "stripe": "Stripe", "paypal": "PayPal"}, "woocommerce": {"title": "WooCommerce", "description": "Automatically sync orders from your WooCommerce store", "apiKey": "API Key", "apiSecret": "API Secret", "storeUrl": "Store URL", "webhookUrl": "Webhook URL"}}, "subscription": {"title": "Subscription", "currentPlan": "Current Plan", "planDetails": "Plan Details", "usage": "Usage", "billing": "Billing", "changePlan": "Change Plan", "cancelSubscription": "Cancel Subscription", "renewSubscription": "Renew Subscription", "invoicesThisMonth": "Invoices This Month", "invoicesLimit": "Invoices Limit", "remainingInvoices": "Remaining Invoices", "nextBilling": "Next Billing", "plans": {"free": "Free", "basic": "Basic", "premium": "Premium", "enterprise": "Enterprise"}, "features": {"invoicesPerMonth": "Invoices per month", "customers": "Customers", "integrations": "Integrations", "support": "Support", "customization": "Customization", "apiAccess": "API Access", "webhooks": "Webhooks", "multiUser": "Multi-user"}}, "company": {"title": "Company", "companyInfo": "Company Information", "taxSettings": "Tax Settings", "invoiceSettings": "Invoice Settings", "bankInfo": "Bank Information", "logo": "Logo", "fields": {"name": "Company Name", "tradeName": "Trade Name", "nif": "NIF", "cif": "CIF", "nie": "NIE", "vatNumber": "VAT Number", "businessType": "Business Type", "sector": "Sector", "defaultTaxRate": "Default Tax Rate", "irpfRate": "IRPF Rate", "invoicePrefix": "Invoice Prefix", "nextNumber": "Next Number", "numberLength": "Number Length", "dueDays": "Due Days", "bankName": "Bank Name", "iban": "IBAN", "swift": "SWIFT Code"}}, "errors": {"generic": "An unexpected error occurred", "network": "Network error", "unauthorized": "Unauthorized", "forbidden": "Access denied", "notFound": "Not found", "validation": "Validation error", "server": "Server error", "timeout": "Request timeout"}, "validation": {"required": "This field is required", "email": "Must be a valid email", "minLength": "Must be at least {min} characters", "maxLength": "Cannot exceed {max} characters", "min": "Must be greater than or equal to {min}", "max": "Must be less than or equal to {max}", "pattern": "Invalid format", "nif": "Invalid NIF", "cif": "Invalid CIF", "nie": "Invalid NIE", "postalCode": "Invalid postal code", "phone": "Invalid phone number", "iban": "Invalid IBAN"}}