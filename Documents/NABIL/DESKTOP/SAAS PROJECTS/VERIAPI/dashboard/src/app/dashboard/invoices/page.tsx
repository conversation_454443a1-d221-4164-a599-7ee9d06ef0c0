'use client';

import { useState, useMemo } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { 
  FileText, 
  Plus, 
  Search, 
  Filter, 
  Download, 
  Eye, 
  Edit, 
  MoreHorizontal,
  Calendar,
  Euro,
  User,
  Building
} from 'lucide-react';
import { formatCurrency, formatDate, getInvoiceStatusColor } from '@/lib/utils';

// Mock data for demonstration
const mockInvoices = [
  {
    id: '1',
    invoiceNumber: 'FAC-000156',
    customer: { 
      id: '1',
      name: 'Empresa ABC S.L.',
      customerType: 'company' as const
    },
    issueDate: '2024-01-15',
    dueDate: '2024-02-14',
    total: 1250.50,
    status: 'paid' as const,
    externalReferences: {
      platform: 'woocommerce',
      externalId: 'WC-12345'
    }
  },
  {
    id: '2',
    invoiceNumber: 'FAC-000155',
    customer: { 
      id: '2',
      name: '<PERSON>',
      customerType: 'individual' as const
    },
    issueDate: '2024-01-14',
    dueDate: '2024-02-13',
    total: 850.00,
    status: 'sent' as const,
    externalReferences: null
  },
  {
    id: '3',
    invoiceNumber: 'FAC-000154',
    customer: { 
      id: '3',
      name: 'Tienda Online S.L.',
      customerType: 'company' as const
    },
    issueDate: '2024-01-10',
    dueDate: '2024-02-09',
    total: 2100.75,
    status: 'overdue' as const,
    externalReferences: {
      platform: 'shopify',
      externalId: 'SH-67890'
    }
  },
  {
    id: '4',
    invoiceNumber: 'FAC-000153',
    customer: { 
      id: '4',
      name: 'María López',
      customerType: 'individual' as const
    },
    issueDate: '2024-01-08',
    dueDate: '2024-02-07',
    total: 450.25,
    status: 'draft' as const,
    externalReferences: null
  },
  {
    id: '5',
    invoiceNumber: 'FAC-000152',
    customer: { 
      id: '5',
      name: 'Consultora XYZ',
      customerType: 'company' as const
    },
    issueDate: '2024-01-05',
    dueDate: '2024-02-04',
    total: 3200.00,
    status: 'viewed' as const,
    externalReferences: null
  }
];

const statusLabels = {
  draft: 'Borrador',
  sent: 'Enviada',
  viewed: 'Vista',
  paid: 'Pagada',
  overdue: 'Vencida',
  cancelled: 'Cancelada'
};

const platformLabels = {
  woocommerce: 'WooCommerce',
  shopify: 'Shopify',
  prestashop: 'PrestaShop',
  manual: 'Manual'
};

export default function InvoicesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [platformFilter, setPlatformFilter] = useState<string>('all');

  // Filter invoices based on search and filters
  const filteredInvoices = useMemo(() => {
    return mockInvoices.filter(invoice => {
      const matchesSearch = 
        invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        invoice.customer.name.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter;
      
      const matchesPlatform = platformFilter === 'all' || 
        (platformFilter === 'manual' && !invoice.externalReferences) ||
        invoice.externalReferences?.platform === platformFilter;

      return matchesSearch && matchesStatus && matchesPlatform;
    });
  }, [searchTerm, statusFilter, platformFilter]);

  // Calculate summary stats
  const stats = useMemo(() => {
    const total = filteredInvoices.reduce((sum, inv) => sum + inv.total, 0);
    const paid = filteredInvoices.filter(inv => inv.status === 'paid').reduce((sum, inv) => sum + inv.total, 0);
    const pending = total - paid;
    
    return {
      total: filteredInvoices.length,
      totalAmount: total,
      paidAmount: paid,
      pendingAmount: pending
    };
  }, [filteredInvoices]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return '✓';
      case 'sent':
        return '📧';
      case 'viewed':
        return '👁';
      case 'overdue':
        return '⚠';
      case 'draft':
        return '📝';
      default:
        return '📄';
    }
  };

  const getPlatformIcon = (platform?: string) => {
    if (!platform) return '✏️';
    switch (platform) {
      case 'woocommerce':
        return '🛒';
      case 'shopify':
        return '🏪';
      case 'prestashop':
        return '🛍';
      default:
        return '🔗';
    }
  };

  return (
    <DashboardLayout title="Facturas">
      <div className="space-y-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Facturas</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Importe Total</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalAmount)}</p>
                </div>
                <Euro className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pagado</p>
                  <p className="text-2xl font-bold text-green-600">{formatCurrency(stats.paidAmount)}</p>
                </div>
                <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 font-bold">✓</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pendiente</p>
                  <p className="text-2xl font-bold text-orange-600">{formatCurrency(stats.pendingAmount)}</p>
                </div>
                <div className="h-8 w-8 bg-orange-100 rounded-full flex items-center justify-center">
                  <span className="text-orange-600 font-bold">⏳</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Actions */}
        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Lista de Facturas
              </CardTitle>
              <Button className="flex items-center">
                <Plus className="h-4 w-4 mr-2" />
                Nueva Factura
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Buscar por número de factura o cliente..."
                  className="input pl-10 w-full"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <select
                className="input w-full sm:w-auto"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="all">Todos los estados</option>
                <option value="draft">Borrador</option>
                <option value="sent">Enviada</option>
                <option value="viewed">Vista</option>
                <option value="paid">Pagada</option>
                <option value="overdue">Vencida</option>
              </select>

              <select
                className="input w-full sm:w-auto"
                value={platformFilter}
                onChange={(e) => setPlatformFilter(e.target.value)}
              >
                <option value="all">Todas las plataformas</option>
                <option value="manual">Manual</option>
                <option value="woocommerce">WooCommerce</option>
                <option value="shopify">Shopify</option>
                <option value="prestashop">PrestaShop</option>
              </select>
            </div>

            {/* Invoices Table */}
            <div className="overflow-x-auto">
              <table className="table">
                <thead className="table-header">
                  <tr className="table-row">
                    <th className="table-head">Factura</th>
                    <th className="table-head">Cliente</th>
                    <th className="table-head">Fecha</th>
                    <th className="table-head">Vencimiento</th>
                    <th className="table-head">Importe</th>
                    <th className="table-head">Estado</th>
                    <th className="table-head">Origen</th>
                    <th className="table-head">Acciones</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredInvoices.map((invoice) => (
                    <tr key={invoice.id} className="table-row">
                      <td className="table-cell">
                        <div className="flex items-center">
                          <FileText className="h-4 w-4 text-gray-400 mr-2" />
                          <span className="font-medium">{invoice.invoiceNumber}</span>
                        </div>
                      </td>
                      <td className="table-cell">
                        <div className="flex items-center">
                          {invoice.customer.customerType === 'company' ? (
                            <Building className="h-4 w-4 text-gray-400 mr-2" />
                          ) : (
                            <User className="h-4 w-4 text-gray-400 mr-2" />
                          )}
                          <span>{invoice.customer.name}</span>
                        </div>
                      </td>
                      <td className="table-cell">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                          {formatDate(invoice.issueDate)}
                        </div>
                      </td>
                      <td className="table-cell">{formatDate(invoice.dueDate)}</td>
                      <td className="table-cell font-medium">{formatCurrency(invoice.total)}</td>
                      <td className="table-cell">
                        <Badge className={getInvoiceStatusColor(invoice.status)}>
                          <span className="mr-1">{getStatusIcon(invoice.status)}</span>
                          {statusLabels[invoice.status]}
                        </Badge>
                      </td>
                      <td className="table-cell">
                        <div className="flex items-center">
                          <span className="mr-1">
                            {getPlatformIcon(invoice.externalReferences?.platform)}
                          </span>
                          <span className="text-sm">
                            {invoice.externalReferences?.platform 
                              ? platformLabels[invoice.externalReferences.platform] 
                              : 'Manual'
                            }
                          </span>
                        </div>
                      </td>
                      <td className="table-cell">
                        <div className="flex items-center space-x-2">
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Download className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredInvoices.length === 0 && (
              <div className="text-center py-12">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No se encontraron facturas</h3>
                <p className="text-gray-500 mb-4">
                  {searchTerm || statusFilter !== 'all' || platformFilter !== 'all'
                    ? 'Intenta ajustar los filtros de búsqueda'
                    : 'Crea tu primera factura para comenzar'
                  }
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Nueva Factura
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
