'use client';

import { useState, useMemo, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import {
  Users,
  Plus,
  Search,
  Building,
  User,
  Mail,
  Phone,
  MapPin,
  Edit,
  Eye,
  MoreHorizontal,
  Calendar,
  Euro,
  FileText,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { formatCurrency, formatDate } from '@/lib/utils';
import { customersApi } from '@/lib/api';
import { toast } from 'react-hot-toast';

export default function CustomersPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [customers, setCustomers] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    companies: 0,
    individuals: 0
  });

  useEffect(() => {
    fetchCustomers();
  }, [currentPage, searchTerm, selectedType]);

  const fetchCustomers = async () => {
    try {
      setLoading(true);
      const response = await customersApi.getCustomers({
        page: currentPage,
        limit: 10,
        search: searchTerm || undefined,
        isActive: selectedType === 'active' ? true : selectedType === 'inactive' ? false : undefined
      });

      setCustomers(response.customers || []);
      setTotalPages(response.pages || 1);

      // Calculate stats
      const totalCustomers = response.total || 0;
      const activeCustomers = response.customers?.filter((c: any) => c.isActive).length || 0;
      const companies = response.customers?.filter((c: any) => c.businessType !== 'individual').length || 0;
      const individuals = response.customers?.filter((c: any) => c.businessType === 'individual').length || 0;

      setStats({
        total: totalCustomers,
        active: activeCustomers,
        companies,
        individuals
      });

    } catch (error) {
      console.error('Error fetching customers:', error);
      toast.error('Error al cargar los clientes');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCustomer = () => {
    router.push('/dashboard/customers/create');
  };

  const handleEditCustomer = (customerId: string) => {
    router.push(`/dashboard/customers/${customerId}/edit`);
  };

  const handleViewCustomer = (customerId: string) => {
    router.push(`/dashboard/customers/${customerId}`);
  };

  if (loading) {
    return (
      <DashboardLayout title="Clientes">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Cargando clientes...</span>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout title="Clientes">
      <div className="space-y-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Clientes</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                </div>
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Clientes Activos</p>
                  <p className="text-2xl font-bold text-green-600">{stats.active}</p>
                </div>
                <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 font-bold">✓</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Empresas</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.companies}</p>
                </div>
                <Building className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Particulares</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.individuals}</p>
                </div>
                <User className="h-8 w-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Customers List */}
        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Lista de Clientes
              </CardTitle>
              <Button onClick={handleCreateCustomer} className="flex items-center">
                <Plus className="h-4 w-4 mr-2" />
                Nuevo Cliente
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Buscar por nombre, email o NIF..."
                  className="input pl-10 w-full"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <select
                className="input w-full sm:w-auto"
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
              >
                <option value="all">Todos los estados</option>
                <option value="active">Activos</option>
                <option value="inactive">Inactivos</option>
              </select>
            </div>

            {/* Customers Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {customers.length > 0 ? (
                customers.map((customer) => (
                  <Card key={customer.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center">
                          {customer.businessType !== 'individual' ? (
                            <Building className="h-8 w-8 text-blue-600 mr-3" />
                          ) : (
                            <User className="h-8 w-8 text-green-600 mr-3" />
                          )}
                          <div>
                            <h3 className="font-semibold text-gray-900">{customer.name}</h3>
                            <p className="text-sm text-gray-500">{customer.nif || customer.cif || customer.nie || 'Sin NIF'}</p>
                          </div>
                        </div>
                        <Badge variant={customer.isActive ? 'default' : 'secondary'}>
                          {customer.isActive ? 'Activo' : 'Inactivo'}
                        </Badge>
                      </div>

                      <div className="space-y-2 mb-4">
                        <div className="flex items-center text-sm text-gray-600">
                          <Mail className="h-4 w-4 mr-2" />
                          {customer.contactEmail || 'Sin email'}
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <Phone className="h-4 w-4 mr-2" />
                          {customer.contactPhone || 'Sin teléfono'}
                        </div>
                        <div className="flex items-center text-sm text-gray-600">
                          <MapPin className="h-4 w-4 mr-2" />
                          {customer.city}, {customer.postalCode}
                        </div>
                      </div>

                      <div className="border-t pt-4">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <p className="text-gray-500">Facturas</p>
                            <p className="font-semibold">{customer.statsTotalInvoices || 0}</p>
                          </div>
                          <div>
                            <p className="text-gray-500">Total</p>
                            <p className="font-semibold">{formatCurrency(customer.statsTotalAmount || 0)}</p>
                          </div>
                        </div>
                      <div className="mt-2">
                        <p className="text-xs text-gray-500">
                          Cliente desde {formatDate(customer.createdAt)}
                        </p>
                        {customer.statsLastInvoiceDate && (
                          <p className="text-xs text-gray-500">
                            Última factura: {formatDate(customer.statsLastInvoiceDate)}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center justify-between mt-4 pt-4 border-t">
                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewCustomer(customer.id)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditCustomer(customer.id)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                      <Button
                        size="sm"
                        onClick={() => router.push(`/dashboard/invoices/create?customerId=${customer.id}`)}
                      >
                        <FileText className="h-4 w-4 mr-1" />
                        Nueva Factura
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))
              ) : (
                <div className="text-center py-12">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No se encontraron clientes</h3>
                  <p className="text-gray-500 mb-4">
                    {searchTerm || selectedType !== 'all'
                      ? 'Intenta ajustar los filtros de búsqueda'
                      : 'Añade tu primer cliente para comenzar'
                  }
                </p>
                <Button onClick={handleCreateCustomer}>
                  <Plus className="h-4 w-4 mr-2" />
                  Nuevo Cliente
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
