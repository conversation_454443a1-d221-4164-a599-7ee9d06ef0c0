'use client';

import { useState, useMemo } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { 
  Users, 
  Plus, 
  Search, 
  Building, 
  User, 
  Mail, 
  Phone, 
  MapPin,
  Edit,
  Eye,
  MoreHorizontal,
  Calendar,
  Euro,
  FileText
} from 'lucide-react';
import { formatCurrency, formatDate } from '@/lib/utils';

// Mock data for demonstration
const mockCustomers = [
  {
    id: '1',
    name: 'Empresa ABC S.L.',
    customerType: 'company' as const,
    nif: '*********',
    contact: {
      email: '<EMAIL>',
      phone: '*********'
    },
    address: {
      street: 'Calle Mayor 123',
      city: 'Madrid',
      postalCode: '28001',
      country: 'España'
    },
    customerSince: '2023-06-15',
    stats: {
      totalInvoices: 15,
      totalAmount: 18750.50,
      lastInvoiceDate: '2024-01-15'
    },
    isActive: true
  },
  {
    id: '2',
    name: '<PERSON>',
    customerType: 'individual' as const,
    nif: '12345678Z',
    contact: {
      email: '<EMAIL>',
      phone: '*********'
    },
    address: {
      street: 'Avenida de la Paz 45',
      city: 'Barcelona',
      postalCode: '08001',
      country: 'España'
    },
    customerSince: '2023-08-22',
    stats: {
      totalInvoices: 8,
      totalAmount: 6800.00,
      lastInvoiceDate: '2024-01-14'
    },
    isActive: true
  },
  {
    id: '3',
    name: 'Tienda Online S.L.',
    customerType: 'company' as const,
    nif: 'B87654321',
    contact: {
      email: '<EMAIL>',
      phone: '*********'
    },
    address: {
      street: 'Paseo de Gracia 88',
      city: 'Barcelona',
      postalCode: '08008',
      country: 'España'
    },
    customerSince: '2023-03-10',
    stats: {
      totalInvoices: 22,
      totalAmount: 46200.75,
      lastInvoiceDate: '2024-01-10'
    },
    isActive: true
  },
  {
    id: '4',
    name: 'María López',
    customerType: 'individual' as const,
    nif: '87654321Y',
    contact: {
      email: '<EMAIL>',
      phone: '*********'
    },
    address: {
      street: 'Calle del Sol 12',
      city: 'Valencia',
      postalCode: '46001',
      country: 'España'
    },
    customerSince: '2023-11-05',
    stats: {
      totalInvoices: 3,
      totalAmount: 1350.75,
      lastInvoiceDate: '2024-01-08'
    },
    isActive: true
  },
  {
    id: '5',
    name: 'Consultora XYZ',
    customerType: 'company' as const,
    nif: 'B11223344',
    contact: {
      email: '<EMAIL>',
      phone: '*********'
    },
    address: {
      street: 'Gran Vía 100',
      city: 'Madrid',
      postalCode: '28013',
      country: 'España'
    },
    customerSince: '2023-01-20',
    stats: {
      totalInvoices: 12,
      totalAmount: 38400.00,
      lastInvoiceDate: '2024-01-05'
    },
    isActive: false
  }
];

export default function CustomersPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Filter customers based on search and filters
  const filteredCustomers = useMemo(() => {
    return mockCustomers.filter(customer => {
      const matchesSearch = 
        customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.contact.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        customer.nif.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesType = typeFilter === 'all' || customer.customerType === typeFilter;
      const matchesStatus = statusFilter === 'all' || 
        (statusFilter === 'active' && customer.isActive) ||
        (statusFilter === 'inactive' && !customer.isActive);

      return matchesSearch && matchesType && matchesStatus;
    });
  }, [searchTerm, typeFilter, statusFilter]);

  // Calculate summary stats
  const stats = useMemo(() => {
    const activeCustomers = filteredCustomers.filter(c => c.isActive).length;
    const totalRevenue = filteredCustomers.reduce((sum, c) => sum + c.stats.totalAmount, 0);
    const totalInvoices = filteredCustomers.reduce((sum, c) => sum + c.stats.totalInvoices, 0);
    
    return {
      total: filteredCustomers.length,
      active: activeCustomers,
      totalRevenue,
      totalInvoices
    };
  }, [filteredCustomers]);

  return (
    <DashboardLayout title="Clientes">
      <div className="space-y-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Clientes</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
                </div>
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Clientes Activos</p>
                  <p className="text-2xl font-bold text-green-600">{stats.active}</p>
                </div>
                <div className="h-8 w-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 font-bold">✓</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Ingresos Totales</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalRevenue)}</p>
                </div>
                <Euro className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Facturas</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalInvoices}</p>
                </div>
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Customers List */}
        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Lista de Clientes
              </CardTitle>
              <Button className="flex items-center">
                <Plus className="h-4 w-4 mr-2" />
                Nuevo Cliente
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Buscar por nombre, email o NIF..."
                  className="input pl-10 w-full"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <select
                className="input w-full sm:w-auto"
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value)}
              >
                <option value="all">Todos los tipos</option>
                <option value="individual">Particulares</option>
                <option value="company">Empresas</option>
              </select>

              <select
                className="input w-full sm:w-auto"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
              >
                <option value="all">Todos los estados</option>
                <option value="active">Activos</option>
                <option value="inactive">Inactivos</option>
              </select>
            </div>

            {/* Customers Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {filteredCustomers.map((customer) => (
                <Card key={customer.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center">
                        {customer.customerType === 'company' ? (
                          <Building className="h-8 w-8 text-blue-600 mr-3" />
                        ) : (
                          <User className="h-8 w-8 text-green-600 mr-3" />
                        )}
                        <div>
                          <h3 className="font-semibold text-gray-900">{customer.name}</h3>
                          <p className="text-sm text-gray-500">{customer.nif}</p>
                        </div>
                      </div>
                      <Badge variant={customer.isActive ? 'default' : 'secondary'}>
                        {customer.isActive ? 'Activo' : 'Inactivo'}
                      </Badge>
                    </div>

                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-sm text-gray-600">
                        <Mail className="h-4 w-4 mr-2" />
                        {customer.contact.email}
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Phone className="h-4 w-4 mr-2" />
                        {customer.contact.phone}
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <MapPin className="h-4 w-4 mr-2" />
                        {customer.address.city}, {customer.address.postalCode}
                      </div>
                    </div>

                    <div className="border-t pt-4">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">Facturas</p>
                          <p className="font-semibold">{customer.stats.totalInvoices}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Total</p>
                          <p className="font-semibold">{formatCurrency(customer.stats.totalAmount)}</p>
                        </div>
                      </div>
                      <div className="mt-2">
                        <p className="text-xs text-gray-500">
                          Cliente desde {formatDate(customer.customerSince)}
                        </p>
                        <p className="text-xs text-gray-500">
                          Última factura: {formatDate(customer.stats.lastInvoiceDate)}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between mt-4 pt-4 border-t">
                      <div className="flex space-x-2">
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                      <Button size="sm">
                        <FileText className="h-4 w-4 mr-1" />
                        Nueva Factura
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {filteredCustomers.length === 0 && (
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No se encontraron clientes</h3>
                <p className="text-gray-500 mb-4">
                  {searchTerm || typeFilter !== 'all' || statusFilter !== 'all'
                    ? 'Intenta ajustar los filtros de búsqueda'
                    : 'Añade tu primer cliente para comenzar'
                  }
                </p>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Nuevo Cliente
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
