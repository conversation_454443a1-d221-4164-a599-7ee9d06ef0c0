'use client';

import { useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { 
  CreditCard, 
  Check, 
  X, 
  Crown, 
  Zap, 
  Users, 
  FileText, 
  Plug, 
  Shield, 
  Calendar,
  TrendingUp,
  AlertCircle,
  Settings
} from 'lucide-react';
import { formatCurrency, formatDate } from '@/lib/utils';

// Mock subscription data
const currentSubscription = {
  plan: 'premium',
  status: 'active',
  nextBilling: '2024-02-15',
  amount: 29.99,
  currency: 'EUR',
  invoicesUsed: 23,
  invoicesLimit: 100,
  features: {
    invoicesPerMonth: 100,
    customers: 'unlimited',
    integrations: 'all',
    support: 'priority',
    customization: true,
    apiAccess: true,
    webhooks: true,
    multiUser: false
  }
};

const plans = [
  {
    id: 'free',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    price: 0,
    currency: 'EUR',
    period: 'mes',
    description: 'Perfecto para empezar',
    popular: false,
    features: {
      invoicesPerMonth: 10,
      customers: 25,
      integrations: 'basic',
      support: 'email',
      customization: false,
      apiAccess: false,
      webhooks: false,
      multiUser: false
    },
    limitations: [
      'Máximo 10 facturas por mes',
      'Hasta 25 clientes',
      'Soporte por email',
      'Sin personalización'
    ]
  },
  {
    id: 'basic',
    name: 'Básico',
    price: 9.99,
    currency: 'EUR',
    period: 'mes',
    description: 'Para pequeños negocios',
    popular: false,
    features: {
      invoicesPerMonth: 50,
      customers: 100,
      integrations: 'basic',
      support: 'email',
      customization: false,
      apiAccess: true,
      webhooks: false,
      multiUser: false
    },
    limitations: [
      'Máximo 50 facturas por mes',
      'Hasta 100 clientes',
      'Integraciones básicas'
    ]
  },
  {
    id: 'premium',
    name: 'Premium',
    price: 29.99,
    currency: 'EUR',
    period: 'mes',
    description: 'Para empresas en crecimiento',
    popular: true,
    features: {
      invoicesPerMonth: 100,
      customers: 'unlimited',
      integrations: 'all',
      support: 'priority',
      customization: true,
      apiAccess: true,
      webhooks: true,
      multiUser: false
    },
    limitations: []
  },
  {
    id: 'enterprise',
    name: 'Empresarial',
    price: 99.99,
    currency: 'EUR',
    period: 'mes',
    description: 'Para grandes empresas',
    popular: false,
    features: {
      invoicesPerMonth: 'unlimited',
      customers: 'unlimited',
      integrations: 'all',
      support: 'dedicated',
      customization: true,
      apiAccess: true,
      webhooks: true,
      multiUser: true
    },
    limitations: []
  }
];

const featureLabels = {
  invoicesPerMonth: 'Facturas por mes',
  customers: 'Clientes',
  integrations: 'Integraciones',
  support: 'Soporte',
  customization: 'Personalización',
  apiAccess: 'Acceso API',
  webhooks: 'Webhooks',
  multiUser: 'Multi-usuario'
};

export default function SubscriptionPage() {
  const [selectedPlan, setSelectedPlan] = useState(currentSubscription.plan);
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');

  const usagePercentage = (currentSubscription.invoicesUsed / currentSubscription.invoicesLimit) * 100;

  const getFeatureValue = (feature: any) => {
    if (typeof feature === 'boolean') {
      return feature ? <Check className="h-4 w-4 text-green-600" /> : <X className="h-4 w-4 text-gray-400" />;
    }
    if (feature === 'unlimited') {
      return 'Ilimitado';
    }
    if (feature === 'all') {
      return 'Todas';
    }
    if (feature === 'basic') {
      return 'Básicas';
    }
    if (feature === 'email') {
      return 'Email';
    }
    if (feature === 'priority') {
      return 'Prioritario';
    }
    if (feature === 'dedicated') {
      return 'Dedicado';
    }
    return feature;
  };

  const getPlanIcon = (planId: string) => {
    switch (planId) {
      case 'free':
        return <FileText className="h-6 w-6" />;
      case 'basic':
        return <Zap className="h-6 w-6" />;
      case 'premium':
        return <Crown className="h-6 w-6" />;
      case 'enterprise':
        return <Shield className="h-6 w-6" />;
      default:
        return <FileText className="h-6 w-6" />;
    }
  };

  return (
    <DashboardLayout title="Suscripción">
      <div className="space-y-6">
        {/* Current Plan Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="h-5 w-5 mr-2" />
                Plan Actual
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <div className="p-3 bg-purple-100 rounded-lg mr-4">
                    <Crown className="h-8 w-8 text-purple-600" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">Plan Premium</h3>
                    <p className="text-gray-600">
                      {formatCurrency(currentSubscription.amount)} / mes
                    </p>
                  </div>
                </div>
                <Badge className="bg-green-100 text-green-800">
                  <Check className="h-3 w-3 mr-1" />
                  Activo
                </Badge>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Uso del Plan</h4>
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span>Facturas este mes</span>
                        <span>{currentSubscription.invoicesUsed} / {currentSubscription.invoicesLimit}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-purple-600 h-2 rounded-full" 
                          style={{ width: `${usagePercentage}%` }}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Facturación</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Próximo cobro:</span>
                      <span className="font-medium">{formatDate(currentSubscription.nextBilling)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Método de pago:</span>
                      <span className="font-medium">•••• 4242</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Estadísticas
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <p className="text-2xl font-bold text-blue-600">156</p>
                  <p className="text-sm text-gray-600">Facturas totales</p>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <p className="text-2xl font-bold text-green-600">€45,230</p>
                  <p className="text-sm text-gray-600">Ingresos totales</p>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <p className="text-2xl font-bold text-purple-600">42</p>
                  <p className="text-sm text-gray-600">Clientes activos</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Plan Comparison */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Cambiar Plan</CardTitle>
              <div className="flex items-center space-x-2">
                <span className={`text-sm ${billingPeriod === 'monthly' ? 'font-medium' : 'text-gray-500'}`}>
                  Mensual
                </span>
                <button
                  onClick={() => setBillingPeriod(billingPeriod === 'monthly' ? 'yearly' : 'monthly')}
                  className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2"
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      billingPeriod === 'yearly' ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
                <span className={`text-sm ${billingPeriod === 'yearly' ? 'font-medium' : 'text-gray-500'}`}>
                  Anual
                  <Badge className="ml-1 bg-green-100 text-green-800 text-xs">-20%</Badge>
                </span>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {plans.map((plan) => {
                const isCurrentPlan = plan.id === currentSubscription.plan;
                const price = billingPeriod === 'yearly' ? plan.price * 12 * 0.8 : plan.price;
                
                return (
                  <Card 
                    key={plan.id} 
                    className={`relative ${plan.popular ? 'ring-2 ring-purple-500' : ''} ${
                      isCurrentPlan ? 'bg-purple-50 border-purple-200' : ''
                    }`}
                  >
                    {plan.popular && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <Badge className="bg-purple-600 text-white">
                          Más Popular
                        </Badge>
                      </div>
                    )}
                    
                    <CardContent className="p-6">
                      <div className="text-center mb-6">
                        <div className="flex justify-center mb-3">
                          {getPlanIcon(plan.id)}
                        </div>
                        <h3 className="text-lg font-bold text-gray-900">{plan.name}</h3>
                        <p className="text-sm text-gray-600 mb-4">{plan.description}</p>
                        <div className="mb-4">
                          <span className="text-3xl font-bold text-gray-900">
                            {plan.price === 0 ? 'Gratis' : formatCurrency(price)}
                          </span>
                          {plan.price > 0 && (
                            <span className="text-gray-600">
                              /{billingPeriod === 'yearly' ? 'año' : 'mes'}
                            </span>
                          )}
                        </div>
                      </div>

                      <div className="space-y-3 mb-6">
                        {Object.entries(plan.features).map(([key, value]) => (
                          <div key={key} className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">{featureLabels[key as keyof typeof featureLabels]}</span>
                            <span className="font-medium">{getFeatureValue(value)}</span>
                          </div>
                        ))}
                      </div>

                      <Button 
                        className="w-full"
                        variant={isCurrentPlan ? 'secondary' : 'primary'}
                        disabled={isCurrentPlan}
                      >
                        {isCurrentPlan ? 'Plan Actual' : 'Seleccionar Plan'}
                      </Button>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Billing History */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Historial de Facturación
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                { date: '2024-01-15', amount: 29.99, status: 'paid', invoice: 'INV-2024-001' },
                { date: '2023-12-15', amount: 29.99, status: 'paid', invoice: 'INV-2023-012' },
                { date: '2023-11-15', amount: 29.99, status: 'paid', invoice: 'INV-2023-011' },
              ].map((bill, index) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <div className="p-2 bg-green-100 rounded-lg mr-4">
                      <Check className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{formatCurrency(bill.amount)}</p>
                      <p className="text-sm text-gray-600">{formatDate(bill.date)}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Badge className="bg-green-100 text-green-800">Pagado</Badge>
                    <Button variant="ghost" size="sm">
                      Descargar
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
