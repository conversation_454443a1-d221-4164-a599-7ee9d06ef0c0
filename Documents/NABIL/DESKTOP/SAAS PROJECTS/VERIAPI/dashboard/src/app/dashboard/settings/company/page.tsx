'use client';

import { useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { 
  Building, 
  Save, 
  Upload, 
  Settings, 
  CreditCard, 
  FileText,
  Percent,
  Hash
} from 'lucide-react';

// Mock company data
const mockCompany = {
  name: 'Test Company',
  tradeName: 'Test Company S.L.',
  nif: '*********',
  address: {
    street: 'Test Street 123',
    city: 'Madrid',
    postalCode: '28001',
    country: 'España'
  },
  contact: {
    email: '<EMAIL>',
    phone: '*********',
    website: 'https://test.com'
  },
  taxSettings: {
    defaultTaxRate: 21,
    irpfRate: 15
  },
  invoiceSettings: {
    prefix: 'FAC',
    nextNumber: 157,
    numberLength: 6,
    dueDays: 30
  },
  bankInfo: {
    bankName: 'Banco Ejemplo',
    iban: 'ES91 2100 0418 4502 0005 1332'
  }
};

export default function CompanySettingsPage() {
  const [company, setCompany] = useState(mockCompany);
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async () => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsLoading(false);
    // Show success message
  };

  const handleInputChange = (section: string, field: string, value: string) => {
    setCompany(prev => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        [field]: value
      }
    }));
  };

  return (
    <DashboardLayout title="Configuración de Empresa">
      <div className="space-y-6">
        {/* Company Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building className="h-5 w-5 mr-2" />
              Información de la Empresa
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nombre de la Empresa
                </label>
                <input
                  type="text"
                  className="input w-full"
                  value={company.name}
                  onChange={(e) => setCompany(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nombre Comercial
                </label>
                <input
                  type="text"
                  className="input w-full"
                  value={company.tradeName}
                  onChange={(e) => setCompany(prev => ({ ...prev, tradeName: e.target.value }))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  NIF/CIF
                </label>
                <input
                  type="text"
                  className="input w-full"
                  value={company.nif}
                  onChange={(e) => setCompany(prev => ({ ...prev, nif: e.target.value }))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email
                </label>
                <input
                  type="email"
                  className="input w-full"
                  value={company.contact.email}
                  onChange={(e) => handleInputChange('contact', 'email', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Teléfono
                </label>
                <input
                  type="tel"
                  className="input w-full"
                  value={company.contact.phone}
                  onChange={(e) => handleInputChange('contact', 'phone', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Sitio Web
                </label>
                <input
                  type="url"
                  className="input w-full"
                  value={company.contact.website}
                  onChange={(e) => handleInputChange('contact', 'website', e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Address */}
        <Card>
          <CardHeader>
            <CardTitle>Dirección</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Dirección
                </label>
                <input
                  type="text"
                  className="input w-full"
                  value={company.address.street}
                  onChange={(e) => handleInputChange('address', 'street', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Ciudad
                </label>
                <input
                  type="text"
                  className="input w-full"
                  value={company.address.city}
                  onChange={(e) => handleInputChange('address', 'city', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Código Postal
                </label>
                <input
                  type="text"
                  className="input w-full"
                  value={company.address.postalCode}
                  onChange={(e) => handleInputChange('address', 'postalCode', e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tax Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Percent className="h-5 w-5 mr-2" />
              Configuración Fiscal
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tipo de IVA por Defecto (%)
                </label>
                <input
                  type="number"
                  className="input w-full"
                  value={company.taxSettings.defaultTaxRate}
                  onChange={(e) => handleInputChange('taxSettings', 'defaultTaxRate', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tipo de IRPF (%)
                </label>
                <input
                  type="number"
                  className="input w-full"
                  value={company.taxSettings.irpfRate}
                  onChange={(e) => handleInputChange('taxSettings', 'irpfRate', e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Invoice Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Configuración de Facturación
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Prefijo de Factura
                </label>
                <input
                  type="text"
                  className="input w-full"
                  value={company.invoiceSettings.prefix}
                  onChange={(e) => handleInputChange('invoiceSettings', 'prefix', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Próximo Número
                </label>
                <input
                  type="number"
                  className="input w-full"
                  value={company.invoiceSettings.nextNumber}
                  onChange={(e) => handleInputChange('invoiceSettings', 'nextNumber', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Longitud del Número
                </label>
                <input
                  type="number"
                  className="input w-full"
                  value={company.invoiceSettings.numberLength}
                  onChange={(e) => handleInputChange('invoiceSettings', 'numberLength', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Días de Vencimiento
                </label>
                <input
                  type="number"
                  className="input w-full"
                  value={company.invoiceSettings.dueDays}
                  onChange={(e) => handleInputChange('invoiceSettings', 'dueDays', e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bank Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <CreditCard className="h-5 w-5 mr-2" />
              Información Bancaria
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nombre del Banco
                </label>
                <input
                  type="text"
                  className="input w-full"
                  value={company.bankInfo.bankName}
                  onChange={(e) => handleInputChange('bankInfo', 'bankName', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  IBAN
                </label>
                <input
                  type="text"
                  className="input w-full"
                  value={company.bankInfo.iban}
                  onChange={(e) => handleInputChange('bankInfo', 'iban', e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button 
            onClick={handleSave}
            loading={isLoading}
            className="flex items-center"
          >
            <Save className="h-4 w-4 mr-2" />
            Guardar Cambios
          </Button>
        </div>
      </div>
    </DashboardLayout>
  );
}
