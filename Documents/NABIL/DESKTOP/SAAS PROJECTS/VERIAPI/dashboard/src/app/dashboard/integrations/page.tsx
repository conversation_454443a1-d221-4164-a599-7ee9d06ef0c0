'use client';

import { useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { 
  Plug, 
  Settings, 
  CheckCircle, 
  XCircle, 
  Clock, 
  ExternalLink,
  Zap,
  ShoppingCart,
  Store,
  CreditCard,
  Globe,
  Webhook,
  Key,
  RefreshCw
} from 'lucide-react';
import { formatDate } from '@/lib/utils';

// Mock data for integrations
const integrations = [
  {
    id: 'woocommerce',
    name: 'WooCommerce',
    description: 'Sincroniza automáticamente los pedidos de tu tienda WooCommerce',
    icon: ShoppingCart,
    color: 'purple',
    status: 'connected' as const,
    isActive: true,
    lastSync: '2024-01-15T10:30:00Z',
    stats: {
      totalOrders: 156,
      thisMonth: 23,
      totalRevenue: 18750.50
    },
    config: {
      storeUrl: 'https://mitienda.com',
      apiKey: 'ck_***************',
      webhookUrl: 'https://api.veriapi.com/webhooks/woocommerce'
    }
  },
  {
    id: 'shopify',
    name: 'Shopify',
    description: 'Conecta tu tienda Shopify para facturación automática',
    icon: Store,
    color: 'green',
    status: 'available' as const,
    isActive: false,
    lastSync: null,
    stats: {
      totalOrders: 0,
      thisMonth: 0,
      totalRevenue: 0
    },
    config: null
  },
  {
    id: 'prestashop',
    name: 'PrestaShop',
    description: 'Integración con tu tienda PrestaShop',
    icon: Globe,
    color: 'blue',
    status: 'available' as const,
    isActive: false,
    lastSync: null,
    stats: {
      totalOrders: 0,
      thisMonth: 0,
      totalRevenue: 0
    },
    config: null
  },
  {
    id: 'stripe',
    name: 'Stripe',
    description: 'Facturación automática para pagos de Stripe',
    icon: CreditCard,
    color: 'indigo',
    status: 'coming-soon' as const,
    isActive: false,
    lastSync: null,
    stats: {
      totalOrders: 0,
      thisMonth: 0,
      totalRevenue: 0
    },
    config: null
  },
  {
    id: 'paypal',
    name: 'PayPal',
    description: 'Sincronización con transacciones de PayPal',
    icon: CreditCard,
    color: 'yellow',
    status: 'coming-soon' as const,
    isActive: false,
    lastSync: null,
    stats: {
      totalOrders: 0,
      thisMonth: 0,
      totalRevenue: 0
    },
    config: null
  },
  {
    id: 'api',
    name: 'API REST',
    description: 'Integración personalizada mediante nuestra API REST',
    icon: Zap,
    color: 'orange',
    status: 'connected' as const,
    isActive: true,
    lastSync: '2024-01-15T09:15:00Z',
    stats: {
      totalOrders: 45,
      thisMonth: 8,
      totalRevenue: 5600.00
    },
    config: {
      apiKey: 'vapi_***************',
      webhookUrl: 'https://api.veriapi.com/webhooks/custom'
    }
  }
];

const statusConfig = {
  connected: {
    label: 'Conectado',
    color: 'bg-green-100 text-green-800',
    icon: CheckCircle
  },
  available: {
    label: 'Disponible',
    color: 'bg-gray-100 text-gray-800',
    icon: Clock
  },
  'coming-soon': {
    label: 'Próximamente',
    color: 'bg-yellow-100 text-yellow-800',
    icon: Clock
  },
  error: {
    label: 'Error',
    color: 'bg-red-100 text-red-800',
    icon: XCircle
  }
};

export default function IntegrationsPage() {
  const [selectedIntegration, setSelectedIntegration] = useState<string | null>(null);

  const connectedIntegrations = integrations.filter(i => i.status === 'connected');
  const totalOrders = connectedIntegrations.reduce((sum, i) => sum + i.stats.totalOrders, 0);
  const totalRevenue = connectedIntegrations.reduce((sum, i) => sum + i.stats.totalRevenue, 0);

  const getStatusBadge = (status: string) => {
    const config = statusConfig[status as keyof typeof statusConfig];
    const Icon = config.icon;
    
    return (
      <Badge className={config.color}>
        <Icon className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    );
  };

  const handleConnect = (integrationId: string) => {
    console.log('Connecting to:', integrationId);
    // Here you would implement the connection logic
  };

  const handleDisconnect = (integrationId: string) => {
    console.log('Disconnecting from:', integrationId);
    // Here you would implement the disconnection logic
  };

  const handleConfigure = (integrationId: string) => {
    setSelectedIntegration(integrationId);
    console.log('Configuring:', integrationId);
    // Here you would open a configuration modal
  };

  return (
    <DashboardLayout title="Integraciones">
      <div className="space-y-6">
        {/* Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Integraciones Activas</p>
                  <p className="text-2xl font-bold text-gray-900">{connectedIntegrations.length}</p>
                </div>
                <Plug className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pedidos Sincronizados</p>
                  <p className="text-2xl font-bold text-gray-900">{totalOrders}</p>
                </div>
                <ShoppingCart className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Ingresos Totales</p>
                  <p className="text-2xl font-bold text-gray-900">€{totalRevenue.toLocaleString()}</p>
                </div>
                <CreditCard className="h-8 w-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Integrations Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {integrations.map((integration) => {
            const Icon = integration.icon;
            const isConnected = integration.status === 'connected';
            const isAvailable = integration.status === 'available';
            
            return (
              <Card key={integration.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center">
                      <div className={`p-3 rounded-lg bg-${integration.color}-100 mr-4`}>
                        <Icon className={`h-6 w-6 text-${integration.color}-600`} />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{integration.name}</h3>
                        <p className="text-sm text-gray-500 mt-1">{integration.description}</p>
                      </div>
                    </div>
                    {getStatusBadge(integration.status)}
                  </div>

                  {isConnected && (
                    <div className="bg-gray-50 rounded-lg p-4 mb-4">
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">Total Pedidos</p>
                          <p className="font-semibold">{integration.stats.totalOrders}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Este Mes</p>
                          <p className="font-semibold">{integration.stats.thisMonth}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Ingresos</p>
                          <p className="font-semibold">€{integration.stats.totalRevenue.toLocaleString()}</p>
                        </div>
                      </div>
                      {integration.lastSync && (
                        <div className="mt-3 pt-3 border-t border-gray-200">
                          <div className="flex items-center text-xs text-gray-500">
                            <RefreshCw className="h-3 w-3 mr-1" />
                            Última sincronización: {formatDate(integration.lastSync)}
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div className="flex space-x-2">
                      {isConnected && (
                        <>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleConfigure(integration.id)}
                          >
                            <Settings className="h-4 w-4 mr-1" />
                            Configurar
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleDisconnect(integration.id)}
                          >
                            Desconectar
                          </Button>
                        </>
                      )}
                      {isAvailable && (
                        <Button 
                          onClick={() => handleConnect(integration.id)}
                          size="sm"
                        >
                          <Plug className="h-4 w-4 mr-1" />
                          Conectar
                        </Button>
                      )}
                      {integration.status === 'coming-soon' && (
                        <Button variant="outline" size="sm" disabled>
                          Próximamente
                        </Button>
                      )}
                    </div>
                    
                    {isConnected && (
                      <Button variant="ghost" size="sm">
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* API Documentation */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Key className="h-5 w-5 mr-2" />
              Documentación de API
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">API REST</h4>
                <p className="text-sm text-gray-600 mb-4">
                  Integra VeriAPI con cualquier aplicación usando nuestra API REST completa.
                </p>
                <div className="space-y-2">
                  <div className="flex items-center text-sm">
                    <span className="font-medium mr-2">Base URL:</span>
                    <code className="bg-gray-100 px-2 py-1 rounded text-xs">
                      https://api.veriapi.com/v1
                    </code>
                  </div>
                  <div className="flex items-center text-sm">
                    <span className="font-medium mr-2">Autenticación:</span>
                    <code className="bg-gray-100 px-2 py-1 rounded text-xs">
                      Bearer Token
                    </code>
                  </div>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Webhooks</h4>
                <p className="text-sm text-gray-600 mb-4">
                  Recibe notificaciones en tiempo real cuando ocurran eventos importantes.
                </p>
                <div className="space-y-2">
                  <div className="flex items-center text-sm">
                    <Webhook className="h-4 w-4 mr-2 text-gray-400" />
                    <span>invoice.created</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Webhook className="h-4 w-4 mr-2 text-gray-400" />
                    <span>invoice.paid</span>
                  </div>
                  <div className="flex items-center text-sm">
                    <Webhook className="h-4 w-4 mr-2 text-gray-400" />
                    <span>customer.created</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">¿Necesitas ayuda?</h4>
                  <p className="text-sm text-gray-600">
                    Consulta nuestra documentación completa o contacta con soporte.
                  </p>
                </div>
                <div className="flex space-x-3">
                  <Button variant="outline">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Documentación
                  </Button>
                  <Button variant="outline">
                    Soporte
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
