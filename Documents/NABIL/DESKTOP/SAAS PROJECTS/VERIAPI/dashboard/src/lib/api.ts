import axios, { AxiosInstance, AxiosResponse } from 'axios';
import Cookies from 'js-cookie';
import { toast } from 'react-hot-toast';
import type {
  ApiResponse,
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  User,
  Company,
  Customer,
  Invoice,
  DashboardStats,
  InvoiceFilters,
  CustomerFilters,
  CreateCustomerRequest,
  CreateInvoiceRequest,
  Payment,
} from '@/types';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000/api';
const TOKEN_KEY = 'veriapi_token';

// Create axios instance
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: API_BASE_URL,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor to add auth token
  client.interceptors.request.use(
    (config) => {
      console.log('API Request:', config.method?.toUpperCase(), config.url, config.data);
      const token = Cookies.get(TOKEN_KEY);
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      console.error('Request interceptor error:', error);
      return Promise.reject(error);
    }
  );

  // Response interceptor for error handling
  client.interceptors.response.use(
    (response) => {
      console.log('API Response:', response.status, response.config.url, response.data);
      return response;
    },
    (error) => {
      console.error('API Error:', error.response?.status, error.response?.data, error.message);
      const message = error.response?.data?.message || error.response?.data?.error || error.message || 'An error occurred';

      if (error.response?.status === 401) {
        // Unauthorized - clear token and redirect to login
        Cookies.remove(TOKEN_KEY);
        if (typeof window !== 'undefined') {
          window.location.href = '/login';
        }
      } else if (error.response?.status >= 500) {
        toast.error('Server error. Please try again later.');
      } else {
        // Don't show toast here, let the calling component handle it
        console.log('API error message:', message);
      }

      return Promise.reject(error);
    }
  );

  return client;
};

const api = createApiClient();

// Auth API
export const authApi = {
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const response: AxiosResponse<LoginResponse> = await api.post('/auth/login', credentials);
    
    // Store token in cookie
    if (response.data.token) {
      Cookies.set(TOKEN_KEY, response.data.token, { 
        expires: 7, // 7 days
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict'
      });
    }
    
    return response.data;
  },

  register: async (data: RegisterRequest): Promise<LoginResponse> => {
    const response: AxiosResponse<LoginResponse> = await api.post('/auth/register', data);
    
    // Store token in cookie
    if (response.data.token) {
      Cookies.set(TOKEN_KEY, response.data.token, { 
        expires: 7,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict'
      });
    }
    
    return response.data;
  },

  logout: () => {
    Cookies.remove(TOKEN_KEY);
    if (typeof window !== 'undefined') {
      window.location.href = '/login';
    }
  },

  getToken: (): string | undefined => {
    return Cookies.get(TOKEN_KEY);
  },

  isAuthenticated: (): boolean => {
    return !!Cookies.get(TOKEN_KEY);
  },
};

// Company API
export const companyApi = {
  getCompany: async (): Promise<Company> => {
    const response: AxiosResponse<{ company: Company }> = await api.get('/company');
    return response.data.company;
  },

  updateCompany: async (data: Partial<Company>): Promise<Company> => {
    const response: AxiosResponse<{ company: Company }> = await api.put('/company', data);
    return response.data.company;
  },

  getStats: async (): Promise<DashboardStats> => {
    const response: AxiosResponse<{ stats: DashboardStats }> = await api.get('/company/stats');
    return response.data.stats;
  },

  getNextInvoiceNumber: async (): Promise<{ nextInvoiceNumber: string; settings: any }> => {
    const response = await api.get('/company/next-invoice-number');
    return response.data;
  },
};

// Customers API
export const customersApi = {
  getCustomers: async (filters?: CustomerFilters): Promise<{ customers: Customer[]; total: number; page: number; totalPages: number }> => {
    const params = new URLSearchParams();
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());
    if (filters?.search) params.append('search', filters.search);
    if (filters?.customerType) params.append('customerType', filters.customerType);
    if (filters?.isActive !== undefined) params.append('isActive', filters.isActive.toString());

    const response = await api.get(`/customers?${params.toString()}`);
    return response.data;
  },

  getCustomer: async (id: string): Promise<Customer> => {
    const response: AxiosResponse<{ customer: Customer }> = await api.get(`/customers/${id}`);
    return response.data.customer;
  },

  createCustomer: async (data: CreateCustomerRequest): Promise<Customer> => {
    const response: AxiosResponse<{ customer: Customer }> = await api.post('/customers', data);
    return response.data.customer;
  },

  updateCustomer: async (id: string, data: Partial<CreateCustomerRequest>): Promise<Customer> => {
    const response: AxiosResponse<{ customer: Customer }> = await api.put(`/customers/${id}`, data);
    return response.data.customer;
  },

  deleteCustomer: async (id: string): Promise<void> => {
    await api.delete(`/customers/${id}`);
  },
};

// Invoices API
export const invoicesApi = {
  getInvoices: async (filters?: InvoiceFilters): Promise<{ invoices: Invoice[]; total: number; page: number; totalPages: number }> => {
    const params = new URLSearchParams();
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());
    if (filters?.status) params.append('status', filters.status);
    if (filters?.customer) params.append('customer', filters.customer);
    if (filters?.dateFrom) params.append('dateFrom', filters.dateFrom);
    if (filters?.dateTo) params.append('dateTo', filters.dateTo);
    if (filters?.platform) params.append('platform', filters.platform);
    if (filters?.search) params.append('search', filters.search);

    const response = await api.get(`/invoices?${params.toString()}`);
    return response.data;
  },

  getInvoice: async (id: string): Promise<Invoice> => {
    const response: AxiosResponse<{ invoice: Invoice }> = await api.get(`/invoices/${id}`);
    return response.data.invoice;
  },

  createInvoice: async (data: CreateInvoiceRequest): Promise<Invoice> => {
    const response: AxiosResponse<{ invoice: Invoice }> = await api.post('/invoices', data);
    return response.data.invoice;
  },

  updateInvoice: async (id: string, data: Partial<CreateInvoiceRequest>): Promise<Invoice> => {
    const response: AxiosResponse<{ invoice: Invoice }> = await api.put(`/invoices/${id}`, data);
    return response.data.invoice;
  },

  deleteInvoice: async (id: string): Promise<void> => {
    await api.delete(`/invoices/${id}`);
  },

  downloadPdf: async (id: string): Promise<Blob> => {
    const response = await api.get(`/invoices/${id}/pdf`, {
      responseType: 'blob',
    });
    return response.data;
  },

  recordPayment: async (id: string, payment: Omit<Payment, 'createdAt'>): Promise<Invoice> => {
    const response: AxiosResponse<{ invoice: Invoice }> = await api.post(`/invoices/${id}/payment`, payment);
    return response.data.invoice;
  },
};

// Export the main API client for custom requests
export default api;
