# VeriAPI Backend Configuration
NEXT_PUBLIC_API_BASE_URL=http://localhost:3000/api
NEXT_PUBLIC_APP_URL=http://localhost:3001

# Environment
NODE_ENV=development

# JWT Configuration (should match backend)
JWT_SECRET=veriapi-super-secret-key-for-development-only-change-in-production

# Optional: Analytics
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=

# Optional: Error Tracking
NEXT_PUBLIC_SENTRY_DSN=

# Optional: Feature Flags
NEXT_PUBLIC_ENABLE_SUBSCRIPTIONS=false
NEXT_PUBLIC_ENABLE_TEAM_MANAGEMENT=false

# Internationalization
NEXT_PUBLIC_DEFAULT_LOCALE=es
NEXT_PUBLIC_SUPPORTED_LOCALES=es,en
