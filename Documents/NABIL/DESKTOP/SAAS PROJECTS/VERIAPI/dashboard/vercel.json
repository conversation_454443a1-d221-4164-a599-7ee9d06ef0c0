{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm ci", "devCommand": "npm run dev", "env": {"NEXT_PUBLIC_API_BASE_URL": "https://api.soloun.link/api", "NEXT_PUBLIC_APP_URL": "https://app.soloun.link", "NEXT_PUBLIC_DEFAULT_LOCALE": "es", "NEXT_PUBLIC_SUPPORTED_LOCALES": "es,en"}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "redirects": [{"source": "/", "destination": "/dashboard", "permanent": false}], "rewrites": [{"source": "/api/:path*", "destination": "https://api.soloun.link/api/:path*"}]}