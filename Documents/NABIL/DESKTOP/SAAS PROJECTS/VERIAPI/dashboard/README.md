# VeriAPI Dashboard

A modern Next.js dashboard for managing electronic invoicing with VeriAPI backend.

## Features

- 🔐 **JWT Authentication** - Secure login with existing VeriAPI backend
- 📊 **Dashboard Overview** - Invoice statistics, charts, and key metrics
- 📄 **Invoice Management** - View, create, edit, and download invoices
- 👥 **Customer Management** - Manage customer information and relationships
- 🔌 **Integrations** - Connect with WooCommerce, Shopify, and other platforms
- 💳 **Subscription Management** - Handle billing and plan upgrades
- 🌍 **Internationalization** - Spanish and English support
- 📱 **Responsive Design** - Works on desktop, tablet, and mobile
- ⚡ **Modern Stack** - Next.js 14, TypeScript, Tailwind CSS

## Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: React Query + Context API
- **Forms**: React Hook Form + Zod validation
- **Tables**: TanStack Table
- **Charts**: Recharts
- **Icons**: Lucide React
- **Internationalization**: next-intl

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- VeriAPI backend running (see backend setup)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd veriapi/dashboard
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Environment setup**
   ```bash
   cp .env.example .env.local
   ```
   
   Update `.env.local` with your configuration:
   ```env
   NEXT_PUBLIC_API_BASE_URL=http://localhost:3000/api
   NEXT_PUBLIC_APP_URL=http://localhost:3001
   ```

4. **Start the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3001](http://localhost:3001)

### Demo Credentials

For testing purposes, you can use these credentials:
- **Email**: <EMAIL>
- **Password**: password123

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── dashboard/         # Dashboard pages
│   ├── login/            # Authentication pages
│   ├── layout.tsx        # Root layout
│   └── page.tsx          # Home page
├── components/            # React components
│   ├── layout/           # Layout components
│   ├── ui/               # Reusable UI components
│   └── ...
├── hooks/                # Custom React hooks
├── lib/                  # Utility libraries
│   ├── api.ts           # API client
│   └── utils.ts         # Helper functions
├── types/                # TypeScript type definitions
├── messages/             # Internationalization files
└── middleware.ts         # Next.js middleware
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript checks
- `npm run format` - Format code with Prettier

## API Integration

The dashboard connects to the VeriAPI backend using REST endpoints:

- **Authentication**: `/api/auth/login`, `/api/auth/register`
- **Invoices**: `/api/invoices`
- **Customers**: `/api/customers`
- **Company**: `/api/company`

All API calls are handled through the centralized API client in `src/lib/api.ts`.

## Deployment

### Vercel (Recommended)

1. **Connect your repository to Vercel**
2. **Set environment variables**:
   ```
   NEXT_PUBLIC_API_BASE_URL=https://your-api-domain.com/api
   NEXT_PUBLIC_APP_URL=https://your-dashboard-domain.com
   ```
3. **Deploy**

### Docker

```bash
# Build the image
docker build -t veriapi-dashboard .

# Run the container
docker run -p 3001:3000 veriapi-dashboard
```

### Manual Deployment

```bash
# Build the application
npm run build

# Start the production server
npm run start
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NEXT_PUBLIC_API_BASE_URL` | VeriAPI backend URL | `http://localhost:3000/api` |
| `NEXT_PUBLIC_APP_URL` | Dashboard URL | `http://localhost:3001` |
| `NEXT_PUBLIC_DEFAULT_LOCALE` | Default language | `es` |
| `NEXT_PUBLIC_SUPPORTED_LOCALES` | Supported languages | `es,en` |

### Customization

- **Colors**: Modify `tailwind.config.js` and `src/app/globals.css`
- **Translations**: Update files in `src/messages/`
- **API endpoints**: Modify `src/lib/api.ts`
- **Navigation**: Update `src/components/layout/Sidebar.tsx`

## Features Roadmap

- [ ] Real-time notifications
- [ ] Advanced filtering and search
- [ ] Bulk operations
- [ ] Export functionality
- [ ] Team management
- [ ] Advanced analytics
- [ ] Mobile app
- [ ] Offline support

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support, please contact the VeriAPI team or create an issue in the repository.
