# 🚀 VeriAPI - Guía de Inicio Rápido

¡Bienvenido a VeriAPI! Esta guía te ayudará a tener el sistema funcionando en menos de 10 minutos.

## ⚡ Inicio <PERSON><PERSON><PERSON> (5 minutos)

### 1. Instalar MongoDB (si no lo tienes)

**macOS:**
```bash
brew install mongodb-community
brew services start mongodb-community
```

**Ubuntu/Debian:**
```bash
sudo apt-get install mongodb
sudo systemctl start mongod
```

**Windows:**
Descarga desde [mongodb.com](https://www.mongodb.com/try/download/community)

### 2. Configurar y Ejecutar el Backend

```bash
# Navegar al directorio del backend
cd backend

# Las dependencias ya están instaladas, solo configurar
cp .env.example .env

# Iniciar el servidor
npm start
```

¡Listo! El servidor estará corriendo en `http://localhost:3000`

### 3. Verificar que Funciona

```bash
# Probar el endpoint de salud
curl http://localhost:3000/health

# Deberías ver: {"status":"ok","timestamp":"..."}
```

## 🎯 Primer Uso - Crear tu Primera Factura

### 1. Registrar tu Empresa

```bash
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "mipassword123",
    "firstName": "Tu",
    "lastName": "Nombre",
    "companyName": "Mi Empresa S.L.",
    "companyEmail": "<EMAIL>",
    "companyAddress": "Calle Principal 123",
    "companyCity": "Madrid",
    "companyPostalCode": "28001",
    "companyCif": "B12345678"
  }'
```

**Guarda el token que recibes** - lo necesitarás para las siguientes llamadas.

### 2. Crear tu Primer Cliente

```bash
curl -X POST http://localhost:3000/api/customers \
  -H "Authorization: Bearer TU_TOKEN_AQUI" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Juan García",
    "customerType": "individual",
    "nif": "12345678Z",
    "address": {
      "street": "Calle Ejemplo 456",
      "city": "Barcelona",
      "postalCode": "08001",
      "country": "España"
    },
    "contact": {
      "email": "<EMAIL>",
      "phone": "+34 93 123 45 67"
    }
  }'
```

**Guarda el ID del cliente** que recibes.

### 3. Crear tu Primera Factura

```bash
curl -X POST http://localhost:3000/api/invoices \
  -H "Authorization: Bearer TU_TOKEN_AQUI" \
  -H "Content-Type: application/json" \
  -d '{
    "customer": "ID_DEL_CLIENTE_AQUI",
    "lines": [
      {
        "description": "Consultoría de desarrollo web",
        "quantity": 10,
        "unitPrice": 50.00,
        "taxRate": 21
      },
      {
        "description": "Hosting anual",
        "quantity": 1,
        "unitPrice": 120.00,
        "taxRate": 21
      }
    ],
    "notes": "Pago por transferencia bancaria"
  }'
```

### 4. Descargar el PDF de la Factura

```bash
# Usar el ID de la factura que recibiste
curl -H "Authorization: Bearer TU_TOKEN_AQUI" \
     http://localhost:3000/api/invoices/ID_FACTURA_AQUI/pdf \
     --output mi_primera_factura.pdf
```

¡Felicidades! 🎉 Ya tienes tu primera factura generada con VeriAPI.

## 🔧 Configuración del Plugin WordPress

### 1. Instalar el Plugin

1. Copia la carpeta `wordpress-plugin` a `/wp-content/plugins/veriapi-woocommerce`
2. Ve a **Plugins** en WordPress y activa **VeriAPI - Facturación Electrónica**

### 2. Configurar el Plugin

1. Ve a **VeriAPI > Configuración**
2. Introduce:
   - **URL de API**: `http://localhost:3000`
   - **Clave de API**: Genera una desde el backend
3. Configura los datos de tu empresa
4. Guarda la configuración

### 3. Generar API Key

```bash
curl -X POST http://localhost:3000/api/auth/generate-api-key \
  -H "Authorization: Bearer TU_TOKEN_AQUI"
```

Copia la API Key que recibes y pégala en la configuración del plugin.

### 4. Probar la Integración

1. Haz un pedido de prueba en WooCommerce
2. Cambia el estado a "Completado"
3. Ve a **VeriAPI > Facturas** para ver la factura generada automáticamente

## 📱 Endpoints Más Útiles

### Listar Facturas
```bash
curl -H "Authorization: Bearer TU_TOKEN" \
     http://localhost:3000/api/invoices
```

### Listar Clientes
```bash
curl -H "Authorization: Bearer TU_TOKEN" \
     http://localhost:3000/api/customers
```

### Ver Información de tu Empresa
```bash
curl -H "Authorization: Bearer TU_TOKEN" \
     http://localhost:3000/api/company
```

### Estadísticas
```bash
curl -H "Authorization: Bearer TU_TOKEN" \
     http://localhost:3000/api/company/stats
```

## 🛠️ Configuración Avanzada Rápida

### Cambiar Tipos de IVA

```bash
curl -X PUT http://localhost:3000/api/company/tax-settings \
  -H "Authorization: Bearer TU_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "defaultTaxRate": 21,
    "taxRates": [
      {"name": "IVA General", "rate": 21},
      {"name": "IVA Reducido", "rate": 10},
      {"name": "IVA Superreducido", "rate": 4},
      {"name": "Exento", "rate": 0}
    ]
  }'
```

### Configurar Numeración de Facturas

```bash
curl -X PUT http://localhost:3000/api/company/invoice-settings \
  -H "Authorization: Bearer TU_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "prefix": "FAC",
    "nextNumber": 1,
    "numberLength": 6,
    "dueDays": 30
  }'
```

## 🚨 Solución de Problemas Rápidos

### Error: "Cannot connect to MongoDB"
```bash
# Verificar que MongoDB está corriendo
sudo systemctl status mongod

# Si no está corriendo, iniciarlo
sudo systemctl start mongod
```

### Error: "Port 3000 already in use"
```bash
# Cambiar puerto en .env
echo "PORT=3001" >> .env
```

### Error: "JWT Secret not found"
```bash
# Verificar que existe el archivo .env
ls -la .env

# Si no existe, copiarlo
cp .env.example .env
```

## 📚 Próximos Pasos

Una vez que tengas todo funcionando:

1. **Lee la documentación completa**: [docs/](docs/)
2. **Configura tu empresa**: Datos fiscales, IBAN, logo
3. **Personaliza las plantillas**: Modifica `templates/invoice-spanish.html`
4. **Configura el despliegue**: Ver [DEPLOYMENT.md](DEPLOYMENT.md)
5. **Explora la API**: Ver [docs/api/README.md](docs/api/README.md)

## 🆘 ¿Necesitas Ayuda?

- **Documentación**: Revisa la carpeta `docs/`
- **Ejemplos**: Mira los archivos de ejemplo
- **Issues**: Reporta problemas en GitHub
- **Email**: <EMAIL>

---

¡Disfruta usando VeriAPI! 🇪🇸✨
