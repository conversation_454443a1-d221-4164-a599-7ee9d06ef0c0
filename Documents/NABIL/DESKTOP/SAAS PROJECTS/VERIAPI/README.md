# VeriAPI - Sistema de Facturación Electrónica

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js Version](https://img.shields.io/badge/node-%3E%3D18.0.0-brightgreen.svg)](https://nodejs.org/)
[![MongoDB](https://img.shields.io/badge/MongoDB-%3E%3D5.0-green.svg)](https://www.mongodb.com/)

VeriAPI es un sistema completo de facturación electrónica diseñado específicamente para el mercado español, con cumplimiento total de la normativa fiscal vigente.

## 🚀 Características Principales

### ✅ Cumplimiento Fiscal Español
- **Numeración consecutiva** obligatoria de facturas
- **Desglose de IVA** por tipos (General 21%, Reducido 10%, Superreducido 4%)
- **Retención IRPF** para profesionales
- **Operaciones intracomunitarias** con inversión del sujeto pasivo
- **Régimen especial** para diferentes tipos de empresas

### 📊 Gestión Completa
- **Clientes**: Particulares y empresas con validación de NIF/CIF/NIE
- **Facturas**: Creación, edición, envío y seguimiento
- **Pagos**: Registro de pagos parciales y completos
- **Informes**: Estadísticas y exportaciones

### 🔌 Integraciones
- **WooCommerce**: Plugin oficial para WordPress
- **API RESTful**: Integración con cualquier sistema
- **PDF automático**: Generación de facturas profesionales
- **Webhooks**: Notificaciones en tiempo real (próximamente)

### 🛡️ Seguridad y Escalabilidad
- **Autenticación JWT** y API Keys
- **Rate limiting** por usuario
- **Validación exhaustiva** de datos
- **Base de datos MongoDB** escalable

## 📦 Instalación Rápida

### Requisitos Previos
- Node.js 18+
- MongoDB 5.0+
- 1GB RAM mínimo

### Backend API

```bash
# Clonar repositorio
git clone https://github.com/veriapi/veriapi.git
cd veriapi/backend

# Instalar dependencias
npm install

# Configurar variables de entorno
cp .env.example .env
# Editar .env con tu configuración

# Iniciar servidor
npm start
```

### Plugin WordPress

1. Descargar la carpeta `wordpress-plugin`
2. Subir a `/wp-content/plugins/veriapi-woocommerce`
3. Activar desde el admin de WordPress
4. Configurar en **VeriAPI > Configuración**

## 🎯 Uso Básico

### 1. Registro de Empresa

```bash
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Juan",
    "lastName": "Pérez",
    "companyName": "Mi Empresa S.L.",
    "companyEmail": "<EMAIL>",
    "companyAddress": "Calle Principal 123",
    "companyCity": "Madrid",
    "companyPostalCode": "28001",
    "companyCif": "B12345678"
  }'
```

### 2. Crear Cliente

```bash
curl -X POST http://localhost:3000/api/customers \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Juan García",
    "customerType": "individual",
    "nif": "12345678Z",
    "address": {
      "street": "Calle Ejemplo 456",
      "city": "Barcelona",
      "postalCode": "08001",
      "country": "España"
    },
    "contact": {
      "email": "<EMAIL>",
      "phone": "+34 93 123 45 67"
    }
  }'
```

### 3. Crear Factura

```bash
curl -X POST http://localhost:3000/api/invoices \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "customer": "CUSTOMER_ID",
    "lines": [
      {
        "description": "Consultoría de desarrollo web",
        "quantity": 10,
        "unitPrice": 50.00,
        "taxRate": 21
      }
    ],
    "dueDate": "2024-02-15",
    "notes": "Pago por transferencia bancaria"
  }'
```

## 🏗️ Arquitectura

```
VeriAPI/
├── backend/                 # API Node.js
│   ├── src/
│   │   ├── models/         # Modelos de datos (MongoDB)
│   │   ├── routes/         # Endpoints de la API
│   │   ├── middleware/     # Autenticación y validación
│   │   ├── utils/          # Utilidades (PDF, validaciones)
│   │   └── app.js          # Aplicación principal
│   ├── templates/          # Plantillas de facturas
│   └── package.json
├── wordpress-plugin/        # Plugin para WooCommerce
│   ├── includes/           # Clases principales
│   ├── admin/              # Interfaz de administración
│   └── veriapi-woocommerce.php
├── docs/                   # Documentación
│   ├── installation/       # Guía de instalación
│   ├── api/               # Documentación de API
│   └── usage/             # Guía de uso
└── README.md
```

## 📚 Documentación

- **[Guía de Instalación](docs/installation/README.md)**: Instalación paso a paso
- **[Documentación de API](docs/api/README.md)**: Referencia completa de endpoints
- **[Guía de Uso](docs/usage/README.md)**: Ejemplos y mejores prácticas

## 🔧 Configuración Avanzada

### Variables de Entorno

```env
# Configuración básica
NODE_ENV=production
PORT=3000

# Base de datos
DATABASE_URL=mongodb://localhost:27017/veriapi

# JWT
JWT_SECRET=tu-clave-secreta-muy-segura
JWT_EXPIRES_IN=7d

# Empresa por defecto
DEFAULT_COMPANY_NAME=Tu Empresa S.L.
DEFAULT_COMPANY_NIF=B12345678
```

### Tipos de IVA Personalizados

```javascript
// Configurar tipos de IVA específicos
const taxRates = [
  { name: "IVA General", rate: 21, description: "Tipo general" },
  { name: "IVA Reducido", rate: 10, description: "Tipo reducido" },
  { name: "IVA Superreducido", rate: 4, description: "Tipo superreducido" },
  { name: "Exento", rate: 0, description: "Operaciones exentas" }
];
```

## 🧪 Testing

```bash
# Ejecutar tests
npm test

# Tests con cobertura
npm run test:coverage

# Tests de integración
npm run test:integration
```

## 🚀 Despliegue en Producción

### Con PM2

```bash
# Instalar PM2
npm install -g pm2

# Iniciar aplicación
pm2 start src/app.js --name veriapi

# Configurar inicio automático
pm2 startup
pm2 save
```

### Con Docker

```bash
# Construir imagen
docker build -t veriapi .

# Ejecutar contenedor
docker run -d -p 3000:3000 --name veriapi veriapi
```

## 🤝 Contribuir

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para detalles.

## 🆘 Soporte

- **Documentación**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/veriapi/veriapi/issues)
- **Email**: <EMAIL>
- **Discord**: [Comunidad VeriAPI](https://discord.gg/veriapi)

## 🗺️ Roadmap

### v1.1 (Q1 2024)
- [ ] Facturae XML (formato estándar español)
- [ ] Integración SII (Suministro Inmediato de Información)
- [ ] Webhooks para notificaciones
- [ ] Dashboard mejorado con gráficos

### v1.2 (Q2 2024)
- [ ] Plugin para Shopify
- [ ] Plugin para PrestaShop
- [ ] API de contabilidad
- [ ] Facturación recurrente

### v2.0 (Q3 2024)
- [ ] Interfaz web completa
- [ ] Multi-empresa
- [ ] Roles y permisos avanzados
- [ ] Integración con bancos

## 🏆 Casos de Uso

### E-commerce
- **WooCommerce**: Facturación automática de pedidos
- **Shopify**: Sincronización de ventas (próximamente)
- **PrestaShop**: Integración nativa (próximamente)

### Servicios Profesionales
- **Consultorías**: Facturación con IRPF
- **Agencias**: Gestión de múltiples clientes
- **Freelancers**: Facturación simplificada

### Empresas
- **PYMES**: Cumplimiento fiscal completo
- **Startups**: Escalabilidad garantizada
- **Corporaciones**: API para sistemas ERP

---

**VeriAPI** - Facturación electrónica moderna para España 🇪🇸
