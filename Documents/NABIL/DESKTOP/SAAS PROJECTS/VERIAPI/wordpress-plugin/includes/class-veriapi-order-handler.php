<?php
/**
 * VeriAPI Order Handler
 *
 * @package VeriAPI_WooCommerce
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * VeriAPI Order Handler Class
 */
class VeriAPI_Order_Handler {

    /**
     * API client
     *
     * @var VeriAPI_API
     */
    private $api;

    /**
     * Constructor
     */
    public function __construct() {
        $this->api = new VeriAPI_API();
    }

    /**
     * Create invoice for order
     *
     * @param int $order_id Order ID
     * @return array
     */
    public function create_invoice($order_id) {
        global $wpdb;

        $order = wc_get_order($order_id);
        if (!$order) {
            return array(
                'success' => false,
                'message' => __('Pedido no encontrado.', 'veriapi-woocommerce')
            );
        }

        // Check if invoice already exists
        $table_name = $wpdb->prefix . 'veriapi_invoices';
        $existing_invoice = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE order_id = %d",
            $order_id
        ));

        if ($existing_invoice) {
            return array(
                'success' => false,
                'message' => __('Ya existe una factura para este pedido.', 'veriapi-woocommerce'),
                'invoice_number' => $existing_invoice->invoice_number
            );
        }

        try {
            // Get or create customer
            $customer_result = $this->get_or_create_customer($order);
            if (!$customer_result['success']) {
                return $customer_result;
            }

            $customer_id = $customer_result['customer_id'];

            // Create invoice
            $invoice_data = $this->api->format_invoice_data($order, $customer_id);
            $response = $this->api->create_invoice($invoice_data);

            if (is_wp_error($response)) {
                throw new Exception($response->get_error_message());
            }

            if ($response['status_code'] !== 201) {
                throw new Exception($response['body']['message'] ?? __('Error desconocido al crear factura.', 'veriapi-woocommerce'));
            }

            $invoice = $response['body']['invoice'];

            // Save invoice to database
            $wpdb->insert(
                $table_name,
                array(
                    'order_id' => $order_id,
                    'invoice_id' => $invoice['id'],
                    'invoice_number' => $invoice['invoiceNumber'],
                    'invoice_url' => $this->api->get_invoice_pdf_url($invoice['id']),
                    'status' => 'completed',
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                ),
                array('%d', '%s', '%s', '%s', '%s', '%s', '%s')
            );

            // Add order note
            $order->add_order_note(sprintf(
                __('Factura VeriAPI creada: %s', 'veriapi-woocommerce'),
                $invoice['invoiceNumber']
            ));

            // Add order meta
            $order->update_meta_data('_veriapi_invoice_id', $invoice['id']);
            $order->update_meta_data('_veriapi_invoice_number', $invoice['invoiceNumber']);
            $order->save();

            return array(
                'success' => true,
                'message' => sprintf(__('Factura %s creada exitosamente.', 'veriapi-woocommerce'), $invoice['invoiceNumber']),
                'invoice_id' => $invoice['id'],
                'invoice_number' => $invoice['invoiceNumber'],
                'invoice_url' => $this->api->get_invoice_pdf_url($invoice['id'])
            );

        } catch (Exception $e) {
            // Log error
            error_log('VeriAPI Invoice Creation Error: ' . $e->getMessage());

            // Save failed attempt
            $wpdb->insert(
                $table_name,
                array(
                    'order_id' => $order_id,
                    'invoice_id' => '',
                    'invoice_number' => '',
                    'status' => 'failed',
                    'created_at' => current_time('mysql'),
                    'updated_at' => current_time('mysql')
                ),
                array('%d', '%s', '%s', '%s', '%s', '%s')
            );

            return array(
                'success' => false,
                'message' => sprintf(__('Error al crear factura: %s', 'veriapi-woocommerce'), $e->getMessage())
            );
        }
    }

    /**
     * Get or create customer
     *
     * @param WC_Order $order Order object
     * @return array
     */
    private function get_or_create_customer($order) {
        $customer_sync = new VeriAPI_Customer_Sync();
        
        // Try to find existing customer by WooCommerce ID
        $wc_customer_id = $order->get_customer_id();
        if ($wc_customer_id > 0) {
            $customer_id = $customer_sync->get_veriapi_customer_id($wc_customer_id);
            if ($customer_id) {
                return array(
                    'success' => true,
                    'customer_id' => $customer_id
                );
            }
        }

        // Try to find by email
        $email = $order->get_billing_email();
        if (!empty($email)) {
            $response = $this->api->get_customers(array('search' => $email));
            if (!is_wp_error($response) && $response['status_code'] === 200) {
                $customers = $response['body']['customers'];
                foreach ($customers as $customer) {
                    if ($customer['contact']['email'] === $email) {
                        // Update mapping
                        if ($wc_customer_id > 0) {
                            $customer_sync->save_customer_mapping($wc_customer_id, $customer['id']);
                        }
                        return array(
                            'success' => true,
                            'customer_id' => $customer['id']
                        );
                    }
                }
            }
        }

        // Create new customer
        return $this->create_customer_from_order($order);
    }

    /**
     * Create customer from order
     *
     * @param WC_Order $order Order object
     * @return array
     */
    private function create_customer_from_order($order) {
        $customer_data = array(
            'name' => trim($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()),
            'customerType' => 'individual',
            'address' => array(
                'street' => $order->get_billing_address_1(),
                'number' => '',
                'city' => $order->get_billing_city(),
                'province' => $order->get_billing_state(),
                'postalCode' => $order->get_billing_postcode(),
                'country' => $order->get_billing_country()
            ),
            'contact' => array(
                'email' => $order->get_billing_email(),
                'phone' => $order->get_billing_phone()
            ),
            'externalIds' => array(
                'woocommerce' => (string) $order->get_customer_id()
            )
        );

        // Add company name if available
        if (!empty($order->get_billing_company())) {
            $customer_data['tradeName'] = $order->get_billing_company();
            $customer_data['customerType'] = 'company';
        }

        // Add Spanish tax ID if available
        $nif = $order->get_meta('_billing_nif');
        if (!empty($nif)) {
            $customer_data = $this->add_spanish_tax_id($customer_data, $nif);
        }

        // Set customer type from meta
        $company_type = $order->get_meta('_billing_company_type');
        if (!empty($company_type)) {
            $customer_data['customerType'] = $company_type;
        }

        $response = $this->api->create_customer($customer_data);

        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => sprintf(__('Error al crear cliente: %s', 'veriapi-woocommerce'), $response->get_error_message())
            );
        }

        if ($response['status_code'] !== 201) {
            return array(
                'success' => false,
                'message' => sprintf(__('Error al crear cliente: %s', 'veriapi-woocommerce'), $response['body']['message'] ?? 'Error desconocido')
            );
        }

        $customer = $response['body']['customer'];

        // Save customer mapping
        if ($order->get_customer_id() > 0) {
            $customer_sync = new VeriAPI_Customer_Sync();
            $customer_sync->save_customer_mapping($order->get_customer_id(), $customer['id']);
        }

        return array(
            'success' => true,
            'customer_id' => $customer['id']
        );
    }

    /**
     * Add Spanish tax ID to customer data
     *
     * @param array $data Customer data
     * @param string $tax_id Tax ID
     * @return array
     */
    private function add_spanish_tax_id($data, $tax_id) {
        $tax_id = strtoupper(trim($tax_id));

        // Detect tax ID type
        if (preg_match('/^[0-9]{8}[TRWAGMYFPDXBNJZSQVHLCKE]$/', $tax_id)) {
            $data['nif'] = $tax_id;
        } elseif (preg_match('/^[ABCDEFGHJNPQRSUVW][0-9]{7}[0-9A-J]$/', $tax_id)) {
            $data['cif'] = $tax_id;
        } elseif (preg_match('/^[XYZ][0-9]{7}[TRWAGMYFPDXBNJZSQVHLCKE]$/', $tax_id)) {
            $data['nie'] = $tax_id;
        }

        return $data;
    }

    /**
     * Get invoice for order
     *
     * @param int $order_id Order ID
     * @return array|null
     */
    public function get_invoice_for_order($order_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'veriapi_invoices';
        
        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $table_name WHERE order_id = %d",
            $order_id
        ), ARRAY_A);
    }

    /**
     * Update invoice status
     *
     * @param int $order_id Order ID
     * @param string $status New status
     * @return bool
     */
    public function update_invoice_status($order_id, $status) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'veriapi_invoices';
        
        return $wpdb->update(
            $table_name,
            array(
                'status' => $status,
                'updated_at' => current_time('mysql')
            ),
            array('order_id' => $order_id),
            array('%s', '%s'),
            array('%d')
        ) !== false;
    }

    /**
     * Delete invoice
     *
     * @param int $order_id Order ID
     * @return bool
     */
    public function delete_invoice($order_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'veriapi_invoices';
        
        return $wpdb->delete(
            $table_name,
            array('order_id' => $order_id),
            array('%d')
        ) !== false;
    }

    /**
     * Retry failed invoices
     *
     * @return array
     */
    public function retry_failed_invoices() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'veriapi_invoices';
        
        $failed_invoices = $wpdb->get_results(
            "SELECT * FROM $table_name WHERE status = 'failed' ORDER BY created_at DESC LIMIT 10"
        );

        $results = array(
            'success' => 0,
            'failed' => 0,
            'errors' => array()
        );

        foreach ($failed_invoices as $invoice_record) {
            // Delete failed record first
            $wpdb->delete(
                $table_name,
                array('id' => $invoice_record->id),
                array('%d')
            );

            // Retry creation
            $result = $this->create_invoice($invoice_record->order_id);
            
            if ($result['success']) {
                $results['success']++;
            } else {
                $results['failed']++;
                $results['errors'][] = sprintf(
                    __('Pedido #%d: %s', 'veriapi-woocommerce'),
                    $invoice_record->order_id,
                    $result['message']
                );
            }
        }

        return $results;
    }
}
