<?php
/**
 * VeriAPI Customer Sync
 *
 * @package VeriAPI_WooCommerce
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * VeriAPI Customer Sync Class
 */
class VeriAPI_Customer_Sync {

    /**
     * API client
     *
     * @var VeriAPI_API
     */
    private $api;

    /**
     * Constructor
     */
    public function __construct() {
        $this->api = new VeriAPI_API();
    }

    /**
     * Sync all customers
     *
     * @return array
     */
    public function sync_all_customers() {
        $customers = get_users(array(
            'role' => 'customer',
            'number' => -1,
            'meta_query' => array(
                array(
                    'key' => 'billing_email',
                    'compare' => 'EXISTS'
                )
            )
        ));

        $results = array(
            'total' => count($customers),
            'synced' => 0,
            'updated' => 0,
            'errors' => 0,
            'error_messages' => array()
        );

        $customer_data = array();

        foreach ($customers as $customer) {
            try {
                $formatted_customer = $this->format_wc_customer($customer);
                if ($formatted_customer) {
                    $customer_data[] = $formatted_customer;
                }
            } catch (Exception $e) {
                $results['errors']++;
                $results['error_messages'][] = sprintf(
                    __('Error procesando cliente %s: %s', 'veriapi-woocommerce'),
                    $customer->user_email,
                    $e->getMessage()
                );
            }
        }

        if (!empty($customer_data)) {
            $response = $this->api->import_customers($customer_data);

            if (is_wp_error($response)) {
                $results['errors'] = $results['total'];
                $results['error_messages'][] = $response->get_error_message();
            } elseif ($response['status_code'] === 200) {
                $import_results = $response['body']['results'];
                $results['synced'] = $import_results['imported'];
                $results['updated'] = $import_results['updated'];
                $results['errors'] = count($import_results['errors']);
                
                foreach ($import_results['errors'] as $error) {
                    $results['error_messages'][] = sprintf(
                        __('Cliente %s: %s', 'veriapi-woocommerce'),
                        $error['customer'],
                        $error['error']
                    );
                }

                // Update last sync time
                update_option('veriapi_last_customer_sync', current_time('mysql'));
            } else {
                $results['errors'] = $results['total'];
                $results['error_messages'][] = $response['body']['message'] ?? __('Error desconocido', 'veriapi-woocommerce');
            }
        }

        return $results;
    }

    /**
     * Sync single customer
     *
     * @param int $customer_id WooCommerce customer ID
     * @return array
     */
    public function sync_customer($customer_id) {
        $customer = get_user_by('id', $customer_id);
        if (!$customer) {
            return array(
                'success' => false,
                'message' => __('Cliente no encontrado.', 'veriapi-woocommerce')
            );
        }

        try {
            $customer_data = $this->format_wc_customer($customer);
            if (!$customer_data) {
                return array(
                    'success' => false,
                    'message' => __('Datos de cliente insuficientes.', 'veriapi-woocommerce')
                );
            }

            // Check if customer already exists in VeriAPI
            $veriapi_customer_id = $this->get_veriapi_customer_id($customer_id);
            
            if ($veriapi_customer_id) {
                // Update existing customer
                $response = $this->api->update_customer($veriapi_customer_id, $customer_data);
                $action = 'updated';
            } else {
                // Create new customer
                $response = $this->api->create_customer($customer_data);
                $action = 'created';
            }

            if (is_wp_error($response)) {
                return array(
                    'success' => false,
                    'message' => $response->get_error_message()
                );
            }

            if ($response['status_code'] === 200 || $response['status_code'] === 201) {
                $veriapi_customer = $response['body']['customer'];
                
                // Save customer mapping
                $this->save_customer_mapping($customer_id, $veriapi_customer['id']);

                return array(
                    'success' => true,
                    'message' => sprintf(__('Cliente %s exitosamente.', 'veriapi-woocommerce'), $action),
                    'customer_id' => $veriapi_customer['id']
                );
            } else {
                return array(
                    'success' => false,
                    'message' => $response['body']['message'] ?? __('Error desconocido', 'veriapi-woocommerce')
                );
            }

        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => $e->getMessage()
            );
        }
    }

    /**
     * Format WooCommerce customer for VeriAPI
     *
     * @param WP_User $customer Customer object
     * @return array|null
     */
    private function format_wc_customer($customer) {
        $billing_email = get_user_meta($customer->ID, 'billing_email', true);
        $billing_first_name = get_user_meta($customer->ID, 'billing_first_name', true);
        $billing_last_name = get_user_meta($customer->ID, 'billing_last_name', true);
        $billing_address_1 = get_user_meta($customer->ID, 'billing_address_1', true);
        $billing_city = get_user_meta($customer->ID, 'billing_city', true);
        $billing_postcode = get_user_meta($customer->ID, 'billing_postcode', true);

        // Skip if essential data is missing
        if (empty($billing_email) || empty($billing_first_name) || empty($billing_address_1) || empty($billing_city)) {
            return null;
        }

        $customer_data = array(
            'name' => trim($billing_first_name . ' ' . $billing_last_name),
            'customerType' => 'individual',
            'address' => array(
                'street' => $billing_address_1,
                'number' => '',
                'city' => $billing_city,
                'province' => get_user_meta($customer->ID, 'billing_state', true),
                'postalCode' => $billing_postcode,
                'country' => get_user_meta($customer->ID, 'billing_country', true) ?: 'España'
            ),
            'contact' => array(
                'email' => $billing_email,
                'phone' => get_user_meta($customer->ID, 'billing_phone', true)
            ),
            'externalIds' => array(
                'woocommerce' => (string) $customer->ID
            )
        );

        // Add company name if available
        $billing_company = get_user_meta($customer->ID, 'billing_company', true);
        if (!empty($billing_company)) {
            $customer_data['tradeName'] = $billing_company;
            $customer_data['customerType'] = 'company';
        }

        // Add Spanish tax ID if available
        $nif = get_user_meta($customer->ID, 'billing_nif', true);
        if (!empty($nif)) {
            $customer_data = $this->add_spanish_tax_id($customer_data, $nif);
        }

        return $customer_data;
    }

    /**
     * Add Spanish tax ID to customer data
     *
     * @param array $data Customer data
     * @param string $tax_id Tax ID
     * @return array
     */
    private function add_spanish_tax_id($data, $tax_id) {
        $tax_id = strtoupper(trim($tax_id));

        // Detect tax ID type
        if (preg_match('/^[0-9]{8}[TRWAGMYFPDXBNJZSQVHLCKE]$/', $tax_id)) {
            $data['nif'] = $tax_id;
        } elseif (preg_match('/^[ABCDEFGHJNPQRSUVW][0-9]{7}[0-9A-J]$/', $tax_id)) {
            $data['cif'] = $tax_id;
        } elseif (preg_match('/^[XYZ][0-9]{7}[TRWAGMYFPDXBNJZSQVHLCKE]$/', $tax_id)) {
            $data['nie'] = $tax_id;
        }

        return $data;
    }

    /**
     * Get VeriAPI customer ID for WooCommerce customer
     *
     * @param int $wc_customer_id WooCommerce customer ID
     * @return string|null
     */
    public function get_veriapi_customer_id($wc_customer_id) {
        return get_user_meta($wc_customer_id, '_veriapi_customer_id', true) ?: null;
    }

    /**
     * Save customer mapping
     *
     * @param int $wc_customer_id WooCommerce customer ID
     * @param string $veriapi_customer_id VeriAPI customer ID
     * @return bool
     */
    public function save_customer_mapping($wc_customer_id, $veriapi_customer_id) {
        return update_user_meta($wc_customer_id, '_veriapi_customer_id', $veriapi_customer_id);
    }

    /**
     * Remove customer mapping
     *
     * @param int $wc_customer_id WooCommerce customer ID
     * @return bool
     */
    public function remove_customer_mapping($wc_customer_id) {
        return delete_user_meta($wc_customer_id, '_veriapi_customer_id');
    }

    /**
     * Get sync statistics
     *
     * @return array
     */
    public function get_sync_stats() {
        global $wpdb;

        $total_customers = get_users(array(
            'role' => 'customer',
            'count_total' => true,
            'fields' => 'ID'
        ));

        $synced_customers = $wpdb->get_var(
            "SELECT COUNT(*) FROM {$wpdb->usermeta} WHERE meta_key = '_veriapi_customer_id'"
        );

        $last_sync = get_option('veriapi_last_customer_sync');

        return array(
            'total_customers' => $total_customers,
            'synced_customers' => intval($synced_customers),
            'unsynced_customers' => $total_customers - intval($synced_customers),
            'last_sync' => $last_sync,
            'sync_percentage' => $total_customers > 0 ? round(($synced_customers / $total_customers) * 100, 1) : 0
        );
    }

    /**
     * Schedule customer sync
     */
    public function schedule_sync() {
        if (!wp_next_scheduled('veriapi_sync_customers')) {
            wp_schedule_event(time(), 'daily', 'veriapi_sync_customers');
        }
    }

    /**
     * Unschedule customer sync
     */
    public function unschedule_sync() {
        wp_clear_scheduled_hook('veriapi_sync_customers');
    }

    /**
     * Handle scheduled sync
     */
    public function handle_scheduled_sync() {
        if (get_option('veriapi_sync_customers') === 'yes') {
            $this->sync_all_customers();
        }
    }
}

// Hook for scheduled sync
add_action('veriapi_sync_customers', array(new VeriAPI_Customer_Sync(), 'handle_scheduled_sync'));
