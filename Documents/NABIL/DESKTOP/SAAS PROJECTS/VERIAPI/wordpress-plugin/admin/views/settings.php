<?php
/**
 * VeriAPI Settings Page
 *
 * @package VeriAPI_WooCommerce
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1><?php _e('Configuración de VeriAPI', 'veriapi-woocommerce'); ?></h1>
    
    <div class="veriapi-admin-header">
        <div class="veriapi-logo">
            <h2>VeriAPI - Facturación Electrónica</h2>
            <p><?php _e('Integración completa con WooCommerce para facturación electrónica española', 'veriapi-woocommerce'); ?></p>
        </div>
    </div>

    <form method="post" action="">
        <?php wp_nonce_field('veriapi_settings_nonce'); ?>
        
        <div class="veriapi-settings-tabs">
            <nav class="nav-tab-wrapper">
                <a href="#api-settings" class="nav-tab nav-tab-active"><?php _e('API', 'veriapi-woocommerce'); ?></a>
                <a href="#invoice-settings" class="nav-tab"><?php _e('Facturas', 'veriapi-woocommerce'); ?></a>
                <a href="#company-settings" class="nav-tab"><?php _e('Empresa', 'veriapi-woocommerce'); ?></a>
                <a href="#sync-settings" class="nav-tab"><?php _e('Sincronización', 'veriapi-woocommerce'); ?></a>
            </nav>

            <!-- API Settings Tab -->
            <div id="api-settings" class="tab-content active">
                <h2><?php _e('Configuración de API', 'veriapi-woocommerce'); ?></h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="veriapi_api_url"><?php _e('URL de API', 'veriapi-woocommerce'); ?></label>
                        </th>
                        <td>
                            <input type="url" id="veriapi_api_url" name="veriapi_api_url" 
                                   value="<?php echo esc_attr($api_url); ?>" class="regular-text" 
                                   placeholder="https://api.veriapi.com" required />
                            <p class="description">
                                <?php _e('URL base de tu instancia de VeriAPI (ej: https://api.veriapi.com)', 'veriapi-woocommerce'); ?>
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="veriapi_api_key"><?php _e('Clave de API', 'veriapi-woocommerce'); ?></label>
                        </th>
                        <td>
                            <input type="password" id="veriapi_api_key" name="veriapi_api_key" 
                                   value="<?php echo esc_attr($api_key); ?>" class="regular-text" 
                                   placeholder="vapi_xxxxxxxxxxxxxxxx" required />
                            <button type="button" id="toggle-api-key" class="button">
                                <?php _e('Mostrar/Ocultar', 'veriapi-woocommerce'); ?>
                            </button>
                            <p class="description">
                                <?php _e('Tu clave de API de VeriAPI. Puedes generarla desde el dashboard de VeriAPI.', 'veriapi-woocommerce'); ?>
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row"><?php _e('Estado de Conexión', 'veriapi-woocommerce'); ?></th>
                        <td>
                            <button type="button" id="test-connection" class="button button-secondary">
                                <?php _e('Probar Conexión', 'veriapi-woocommerce'); ?>
                            </button>
                            <div id="connection-status" class="veriapi-status"></div>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Invoice Settings Tab -->
            <div id="invoice-settings" class="tab-content">
                <h2><?php _e('Configuración de Facturas', 'veriapi-woocommerce'); ?></h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="veriapi_auto_create_invoice"><?php _e('Crear Facturas Automáticamente', 'veriapi-woocommerce'); ?></label>
                        </th>
                        <td>
                            <select id="veriapi_auto_create_invoice" name="veriapi_auto_create_invoice">
                                <option value="yes" <?php selected($auto_create, 'yes'); ?>><?php _e('Sí', 'veriapi-woocommerce'); ?></option>
                                <option value="no" <?php selected($auto_create, 'no'); ?>><?php _e('No', 'veriapi-woocommerce'); ?></option>
                            </select>
                            <p class="description">
                                <?php _e('Crear facturas automáticamente cuando los pedidos cambien de estado.', 'veriapi-woocommerce'); ?>
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="veriapi_invoice_on_status"><?php _e('Crear Factura en Estado', 'veriapi-woocommerce'); ?></label>
                        </th>
                        <td>
                            <select id="veriapi_invoice_on_status" name="veriapi_invoice_on_status">
                                <option value="processing" <?php selected($invoice_status, 'processing'); ?>><?php _e('Procesando', 'veriapi-woocommerce'); ?></option>
                                <option value="completed" <?php selected($invoice_status, 'completed'); ?>><?php _e('Completado', 'veriapi-woocommerce'); ?></option>
                            </select>
                            <p class="description">
                                <?php _e('Estado del pedido que activará la creación automática de facturas.', 'veriapi-woocommerce'); ?>
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="veriapi_default_tax_rate"><?php _e('Tipo de IVA por Defecto (%)', 'veriapi-woocommerce'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="veriapi_default_tax_rate" name="veriapi_default_tax_rate" 
                                   value="<?php echo esc_attr($default_tax_rate); ?>" min="0" max="100" step="0.01" />
                            <p class="description">
                                <?php _e('Tipo de IVA a aplicar cuando no se puede determinar desde WooCommerce.', 'veriapi-woocommerce'); ?>
                            </p>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Company Settings Tab -->
            <div id="company-settings" class="tab-content">
                <h2><?php _e('Información de la Empresa', 'veriapi-woocommerce'); ?></h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="veriapi_company_name"><?php _e('Nombre de la Empresa', 'veriapi-woocommerce'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="veriapi_company_name" name="veriapi_company_name" 
                                   value="<?php echo esc_attr($company_name); ?>" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="veriapi_company_email"><?php _e('Email de la Empresa', 'veriapi-woocommerce'); ?></label>
                        </th>
                        <td>
                            <input type="email" id="veriapi_company_email" name="veriapi_company_email" 
                                   value="<?php echo esc_attr($company_email); ?>" class="regular-text" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="veriapi_company_nif"><?php _e('NIF/CIF', 'veriapi-woocommerce'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="veriapi_company_nif" name="veriapi_company_nif" 
                                   value="<?php echo esc_attr($company_nif); ?>" class="regular-text" 
                                   placeholder="B12345678" />
                            <p class="description">
                                <?php _e('Número de identificación fiscal de tu empresa.', 'veriapi-woocommerce'); ?>
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="veriapi_company_address"><?php _e('Dirección', 'veriapi-woocommerce'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="veriapi_company_address" name="veriapi_company_address" 
                                   value="<?php echo esc_attr($company_address); ?>" class="regular-text" 
                                   placeholder="Calle Principal 123" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="veriapi_company_city"><?php _e('Ciudad', 'veriapi-woocommerce'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="veriapi_company_city" name="veriapi_company_city" 
                                   value="<?php echo esc_attr($company_city); ?>" class="regular-text" 
                                   placeholder="Madrid" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="veriapi_company_postcode"><?php _e('Código Postal', 'veriapi-woocommerce'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="veriapi_company_postcode" name="veriapi_company_postcode" 
                                   value="<?php echo esc_attr($company_postcode); ?>" class="regular-text" 
                                   placeholder="28001" pattern="[0-9]{5}" />
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="veriapi_company_phone"><?php _e('Teléfono', 'veriapi-woocommerce'); ?></label>
                        </th>
                        <td>
                            <input type="tel" id="veriapi_company_phone" name="veriapi_company_phone" 
                                   value="<?php echo esc_attr($company_phone); ?>" class="regular-text" 
                                   placeholder="+34 91 123 45 67" />
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Sync Settings Tab -->
            <div id="sync-settings" class="tab-content">
                <h2><?php _e('Configuración de Sincronización', 'veriapi-woocommerce'); ?></h2>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="veriapi_sync_customers"><?php _e('Sincronizar Clientes Automáticamente', 'veriapi-woocommerce'); ?></label>
                        </th>
                        <td>
                            <select id="veriapi_sync_customers" name="veriapi_sync_customers">
                                <option value="yes" <?php selected($sync_customers, 'yes'); ?>><?php _e('Sí', 'veriapi-woocommerce'); ?></option>
                                <option value="no" <?php selected($sync_customers, 'no'); ?>><?php _e('No', 'veriapi-woocommerce'); ?></option>
                            </select>
                            <p class="description">
                                <?php _e('Sincronizar automáticamente los clientes de WooCommerce con VeriAPI diariamente.', 'veriapi-woocommerce'); ?>
                            </p>
                        </td>
                    </tr>
                </table>
                
                <div class="veriapi-sync-actions">
                    <h3><?php _e('Acciones de Sincronización', 'veriapi-woocommerce'); ?></h3>
                    <p>
                        <button type="button" id="sync-all-customers" class="button button-secondary">
                            <?php _e('Sincronizar Todos los Clientes Ahora', 'veriapi-woocommerce'); ?>
                        </button>
                        <span class="description">
                            <?php _e('Esto sincronizará todos los clientes de WooCommerce con VeriAPI.', 'veriapi-woocommerce'); ?>
                        </span>
                    </p>
                    <div id="sync-status" class="veriapi-status"></div>
                </div>
            </div>
        </div>

        <p class="submit">
            <input type="submit" name="submit" id="submit" class="button button-primary" 
                   value="<?php _e('Guardar Configuración', 'veriapi-woocommerce'); ?>" />
        </p>
    </form>
</div>

<script>
jQuery(document).ready(function($) {
    // Tab switching
    $('.nav-tab').on('click', function(e) {
        e.preventDefault();
        var target = $(this).attr('href');
        
        $('.nav-tab').removeClass('nav-tab-active');
        $(this).addClass('nav-tab-active');
        
        $('.tab-content').removeClass('active');
        $(target).addClass('active');
    });
    
    // Toggle API key visibility
    $('#toggle-api-key').on('click', function() {
        var input = $('#veriapi_api_key');
        var type = input.attr('type');
        input.attr('type', type === 'password' ? 'text' : 'password');
    });
    
    // Test connection
    $('#test-connection').on('click', function() {
        var button = $(this);
        var status = $('#connection-status');
        
        button.prop('disabled', true).text(veriapi_admin.strings.testing_connection);
        status.removeClass('success error').html('');
        
        $.ajax({
            url: veriapi_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'veriapi_test_connection',
                nonce: veriapi_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    status.addClass('success').html('<span class="dashicons dashicons-yes"></span> ' + response.message);
                } else {
                    status.addClass('error').html('<span class="dashicons dashicons-no"></span> ' + response.message);
                }
            },
            error: function() {
                status.addClass('error').html('<span class="dashicons dashicons-no"></span> ' + veriapi_admin.strings.connection_failed);
            },
            complete: function() {
                button.prop('disabled', false).text('<?php _e('Probar Conexión', 'veriapi-woocommerce'); ?>');
            }
        });
    });
    
    // Sync customers
    $('#sync-all-customers').on('click', function() {
        if (!confirm(veriapi_admin.strings.confirm_sync)) {
            return;
        }
        
        var button = $(this);
        var status = $('#sync-status');
        
        button.prop('disabled', true).text(veriapi_admin.strings.syncing_customers);
        status.removeClass('success error').html('');
        
        $.ajax({
            url: veriapi_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'veriapi_sync_customers',
                nonce: veriapi_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    status.addClass('success').html(
                        '<span class="dashicons dashicons-yes"></span> ' + 
                        veriapi_admin.strings.sync_complete + 
                        '<br>Sincronizados: ' + response.synced + 
                        ', Actualizados: ' + response.updated + 
                        ', Errores: ' + response.errors
                    );
                } else {
                    status.addClass('error').html('<span class="dashicons dashicons-no"></span> ' + response.message);
                }
            },
            error: function() {
                status.addClass('error').html('<span class="dashicons dashicons-no"></span> Error de sincronización');
            },
            complete: function() {
                button.prop('disabled', false).text('<?php _e('Sincronizar Todos los Clientes Ahora', 'veriapi-woocommerce'); ?>');
            }
        });
    });
});
</script>
